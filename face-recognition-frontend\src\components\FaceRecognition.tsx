import React, { useState, useRef, useCallback, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Webcam from 'react-webcam';
import {
  CameraIcon,
  EyeIcon,
  PlayIcon,
  StopIcon,
  UserIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import { recognitionAPI, base64ToFile } from '../services/api';

interface RecognitionResult {
  id: string;
  name: string;
  confidence: number;
  timestamp: Date;
  status: 'success' | 'failed' | 'unknown';
  department?: string;
  position?: string;
}

const FaceRecognition: React.FC = () => {
  const webcamRef = useRef<Webcam>(null);
  const [isRecognizing, setIsRecognizing] = useState(false);
  const [currentResult, setCurrentResult] = useState<RecognitionResult | null>(null);
  const [recognitionHistory, setRecognitionHistory] = useState<RecognitionResult[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);

  // 模拟实时识别
  useEffect(() => {
    let interval: NodeJS.Timeout;
    
    if (isRecognizing) {
      interval = setInterval(() => {
        performRecognition();
      }, 3000); // 每3秒识别一次
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isRecognizing]);

  const performRecognition = useCallback(async () => {
    if (!webcamRef.current) return;

    setIsProcessing(true);

    try {
      // 捕获当前帧
      const imageSrc = webcamRef.current.getScreenshot();
      if (!imageSrc) {
        setIsProcessing(false);
        return;
      }

      // 将base64图像转换为File对象
      const imageFile = base64ToFile(imageSrc, 'recognition-image.jpg');

      // 调用识别API
      const result = await recognitionAPI.identify(imageFile);

      // 转换API结果为组件需要的格式
      const recognitionResult: RecognitionResult = {
        id: result.user_id || 'unknown',
        name: result.name || '未知用户',
        confidence: result.confidence,
        timestamp: new Date(result.timestamp),
        status: result.status,
        department: undefined, // API暂时不返回这些信息
        position: undefined
      };

      setCurrentResult(recognitionResult);
      setRecognitionHistory(prev => [recognitionResult, ...prev.slice(0, 9)]); // 保留最近10条记录

    } catch (error) {
      console.error('Recognition error:', error);
      // 在错误情况下创建一个失败结果
      const errorResult: RecognitionResult = {
        id: 'error',
        name: '识别失败',
        confidence: 0,
        timestamp: new Date(),
        status: 'failed'
      };
      setCurrentResult(errorResult);
      setRecognitionHistory(prev => [errorResult, ...prev.slice(0, 9)]);
    } finally {
      setIsProcessing(false);
    }
  }, []);

  const startRecognition = () => {
    setIsRecognizing(true);
    setCurrentResult(null);
  };

  const stopRecognition = () => {
    setIsRecognizing(false);
    setIsProcessing(false);
    setCurrentResult(null);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success': return 'text-green-600 dark:text-green-400';
      case 'failed': return 'text-red-600 dark:text-red-400';
      case 'unknown': return 'text-yellow-600 dark:text-yellow-400';
      default: return 'text-gray-600 dark:text-gray-400';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success': return CheckCircleIcon;
      case 'failed': return XCircleIcon;
      case 'unknown': return ExclamationTriangleIcon;
      default: return UserIcon;
    }
  };

  return (
    <div className="max-w-6xl mx-auto space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center"
      >
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white">实时人脸识别</h1>
        <p className="text-gray-600 dark:text-gray-400 mt-2">
          启动摄像头进行实时人脸识别
        </p>
      </motion.div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Camera Section */}
        <div className="lg:col-span-2">
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            className="card p-6"
          >
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white flex items-center">
                <EyeIcon className="w-6 h-6 mr-2" />
                实时识别
              </h2>
              
              <div className="flex space-x-2">
                {!isRecognizing ? (
                  <button
                    onClick={startRecognition}
                    className="btn-primary flex items-center space-x-2"
                  >
                    <PlayIcon className="w-5 h-5" />
                    <span>开始识别</span>
                  </button>
                ) : (
                  <button
                    onClick={stopRecognition}
                    className="bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 flex items-center space-x-2"
                  >
                    <StopIcon className="w-5 h-5" />
                    <span>停止识别</span>
                  </button>
                )}
              </div>
            </div>

            <div className="relative bg-gray-100 dark:bg-dark-700 rounded-lg overflow-hidden aspect-video">
              <Webcam
                ref={webcamRef}
                audio={false}
                className="w-full h-full object-cover"
                videoConstraints={{
                  width: 1280,
                  height: 720,
                  facingMode: "user"
                }}
              />
              
              {/* Recognition Overlay */}
              {isRecognizing && (
                <div className="absolute inset-0">
                  {/* Face Detection Frame */}
                  <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                    <div className={`w-48 h-48 border-2 rounded-lg transition-colors duration-300 ${
                      currentResult?.status === 'success' ? 'border-green-500' :
                      currentResult?.status === 'failed' ? 'border-red-500' :
                      currentResult?.status === 'unknown' ? 'border-yellow-500' :
                      'border-blue-500'
                    }`}>
                      {isProcessing && (
                        <div className="absolute inset-0 bg-black bg-opacity-20 flex items-center justify-center">
                          <div className="w-8 h-8 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Status Indicator */}
                  <div className="absolute top-4 left-4">
                    <div className="flex items-center space-x-2 bg-black bg-opacity-50 text-white px-3 py-2 rounded-lg">
                      <div className={`w-2 h-2 rounded-full ${isRecognizing ? 'bg-red-500 animate-pulse' : 'bg-gray-500'}`}></div>
                      <span className="text-sm font-medium">
                        {isRecognizing ? '识别中...' : '已停止'}
                      </span>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Current Recognition Result */}
            <AnimatePresence>
              {currentResult && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  className="mt-4 p-4 bg-gray-50 dark:bg-dark-700 rounded-lg"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      {React.createElement(getStatusIcon(currentResult.status), {
                        className: `w-6 h-6 ${getStatusColor(currentResult.status)}`
                      })}
                      <div>
                        <p className="font-semibold text-gray-900 dark:text-white">
                          {currentResult.name}
                        </p>
                        {currentResult.department && (
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            {currentResult.department} - {currentResult.position}
                          </p>
                        )}
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium text-gray-900 dark:text-white">
                        置信度: {currentResult.confidence.toFixed(1)}%
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        {currentResult.timestamp.toLocaleTimeString()}
                      </p>
                    </div>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>
        </div>

        {/* Recognition History */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          className="card p-6"
        >
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
            <ClockIcon className="w-5 h-5 mr-2" />
            识别记录
          </h3>
          
          <div className="space-y-3 max-h-96 overflow-y-auto">
            {recognitionHistory.length === 0 ? (
              <p className="text-gray-500 dark:text-gray-400 text-center py-8">
                暂无识别记录
              </p>
            ) : (
              recognitionHistory.map((result, index) => (
                <motion.div
                  key={`${result.id}-${result.timestamp.getTime()}`}
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="p-3 bg-gray-50 dark:bg-dark-700 rounded-lg"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      {React.createElement(getStatusIcon(result.status), {
                        className: `w-4 h-4 ${getStatusColor(result.status)}`
                      })}
                      <div>
                        <p className="text-sm font-medium text-gray-900 dark:text-white">
                          {result.name}
                        </p>
                        <p className="text-xs text-gray-500 dark:text-gray-400">
                          {result.confidence.toFixed(1)}% 置信度
                        </p>
                      </div>
                    </div>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {result.timestamp.toLocaleTimeString()}
                    </p>
                  </div>
                </motion.div>
              ))
            )}
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default FaceRecognition;
