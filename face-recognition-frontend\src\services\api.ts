import axios from 'axios';

const API_BASE_URL = 'http://localhost:8000';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

export interface User {
  id: string;
  name: string;
  email?: string;
  department?: string;
  position?: string;
  registered_at: string;
  status: string;
}

export interface UserCreate {
  name: string;
  email?: string;
  department?: string;
  position?: string;
}

export interface RecognitionResult {
  user_id?: string;
  name?: string;
  confidence: number;
  timestamp: string;
  status: string;
}

export interface AnalyticsStats {
  total_users: number;
  today_recognitions: number;
  success_rate: number;
  active_users: number;
}

// 用户管理API
export const userAPI = {
  // 注册新用户
  register: async (userData: UserCreate, faceImage: File): Promise<User> => {
    const formData = new FormData();
    formData.append('face_image', faceImage);
    
    // 将用户数据作为JSON字符串添加到FormData
    Object.entries(userData).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        formData.append(key, value);
      }
    });

    const response = await api.post('/api/users/register', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  // 获取所有用户
  getAll: async (): Promise<User[]> => {
    const response = await api.get('/api/users');
    return response.data;
  },

  // 删除用户
  delete: async (userId: string): Promise<void> => {
    await api.delete(`/api/users/${userId}`);
  },
};

// 人脸识别API
export const recognitionAPI = {
  // 识别人脸
  identify: async (faceImage: File): Promise<RecognitionResult> => {
    const formData = new FormData();
    formData.append('face_image', faceImage);

    const response = await api.post('/api/recognition/identify', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },
};

// 分析统计API
export const analyticsAPI = {
  // 获取统计数据
  getStats: async (): Promise<AnalyticsStats> => {
    const response = await api.get('/api/analytics/stats');
    return response.data;
  },
};

// 工具函数：将base64图像转换为File对象
export const base64ToFile = (base64String: string, filename: string = 'image.jpg'): File => {
  const arr = base64String.split(',');
  const mime = arr[0].match(/:(.*?);/)?.[1] || 'image/jpeg';
  const bstr = atob(arr[1]);
  let n = bstr.length;
  const u8arr = new Uint8Array(n);
  
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }
  
  return new File([u8arr], filename, { type: mime });
};

// 错误处理
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 404) {
      console.error('API endpoint not found:', error.config?.url);
    } else if (error.response?.status >= 500) {
      console.error('Server error:', error.response?.data?.detail || error.message);
    } else if (error.response?.status >= 400) {
      console.error('Client error:', error.response?.data?.detail || error.message);
    } else if (error.request) {
      console.error('Network error: Unable to connect to server');
    }
    return Promise.reject(error);
  }
);

export default api;
