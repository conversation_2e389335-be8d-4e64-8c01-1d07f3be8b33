# 智能人脸识别系统 - 安装指南

## 🚀 快速安装

### 环境要求
- **Node.js** 16+ (用于前端)
- **Python** 3.8+ (用于后端)
- **摄像头设备** (用于人脸拍摄)

### 方法一：一键启动（推荐）

1. **双击运行启动脚本**
   ```
   start-system.bat
   ```

2. **等待服务启动**
   - 后端服务将在 http://localhost:8000 启动
   - 前端应用将在 http://localhost:5173 启动

3. **访问系统**
   - 打开浏览器访问 http://localhost:5173

### 方法二：手动启动

#### 启动前端
```bash
cd face-recognition-frontend
npm install
npm run dev
```

#### 启动后端
```bash
cd face-recognition-backend
pip install -r requirements.txt
python main.py
```

## 🔧 故障排除

### 常见问题

#### 1. Python依赖安装失败
**问题**: pip install 时出现编译错误

**解决方案**:
```bash
# 升级pip
python -m pip install --upgrade pip

# 安装基础依赖
pip install fastapi uvicorn python-multipart opencv-python numpy Pillow
```

#### 2. OpenCV安装问题
**问题**: opencv-python 安装失败

**解决方案**:
```bash
# 使用预编译版本
pip install opencv-python-headless
```

#### 3. 摄像头权限问题
**问题**: 浏览器无法访问摄像头

**解决方案**:
- 确保浏览器有摄像头权限
- 使用 HTTPS 或 localhost
- 检查摄像头是否被其他应用占用

#### 4. 端口占用问题
**问题**: 端口 5173 或 8000 被占用

**解决方案**:
```bash
# 查看端口占用
netstat -ano | findstr :5173
netstat -ano | findstr :8000

# 结束占用进程
taskkill /PID <进程ID> /F
```

### 系统要求

#### 最低配置
- CPU: 双核 2.0GHz
- 内存: 4GB RAM
- 存储: 2GB 可用空间
- 摄像头: 720p

#### 推荐配置
- CPU: 四核 2.5GHz+
- 内存: 8GB+ RAM
- 存储: 5GB+ 可用空间
- 摄像头: 1080p

## 📝 功能说明

### 系统特色
- ✅ **人脸检测**: 基于OpenCV的实时人脸检测
- ✅ **用户管理**: 完整的用户增删改查功能
- ✅ **数据分析**: 识别统计和趋势分析
- ✅ **响应式界面**: 支持桌面和移动设备
- ✅ **主题切换**: 深色/浅色模式

### 演示模式
当前版本使用演示模式，具有以下特点：
- 人脸检测：真实的OpenCV人脸检测
- 人脸识别：模拟识别结果（70%成功率）
- 数据统计：基于真实的数据库记录
- 用户管理：完整的数据库操作

## 🔄 升级到完整版

如需真实的人脸识别功能，可以安装以下依赖：

```bash
# 安装dlib（需要Visual Studio Build Tools）
pip install dlib

# 安装face_recognition
pip install face_recognition
```

然后替换 `main.py` 中的人脸识别逻辑。

## 📞 技术支持

如遇到问题，请：
1. 查看控制台错误信息
2. 检查网络连接
3. 确认端口未被占用
4. 重启服务

## 🎯 下一步

系统启动后，你可以：
1. 📊 查看仪表板了解系统概况
2. 👤 注册新用户测试人脸检测
3. 🔍 使用实时识别功能
4. 👥 管理系统用户
5. 📈 查看数据分析报告
6. ⚙️ 调整系统设置

享受使用智能人脸识别系统！🎉
