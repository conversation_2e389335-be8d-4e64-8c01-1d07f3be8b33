import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  CogIcon, 
  CameraIcon,
  ShieldCheckIcon,
  BellIcon,
  DatabaseIcon,
  ServerIcon,
  UserIcon,
  KeyIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';

const Settings: React.FC = () => {
  const [settings, setSettings] = useState({
    // 识别设置
    recognitionThreshold: 85,
    maxFaceSize: 500,
    minFaceSize: 50,
    enableLivenessDetection: true,
    
    // 摄像头设置
    cameraResolution: '1280x720',
    frameRate: 30,
    enableAutoFocus: true,
    
    // 安全设置
    enableEncryption: true,
    sessionTimeout: 30,
    maxLoginAttempts: 3,
    enableTwoFactor: false,
    
    // 通知设置
    enableEmailNotifications: true,
    enablePushNotifications: false,
    notifyOnFailedRecognition: true,
    notifyOnNewRegistration: true,
    
    // 数据库设置
    backupFrequency: 'daily',
    retentionPeriod: 90,
    enableAutoCleanup: true,
  });

  const [isSaving, setIsSaving] = useState(false);
  const [saveStatus, setSaveStatus] = useState<'idle' | 'success' | 'error'>('idle');

  const handleSettingChange = (key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleSave = async () => {
    setIsSaving(true);
    setSaveStatus('idle');
    
    try {
      // 模拟保存过程
      await new Promise(resolve => setTimeout(resolve, 1500));
      setSaveStatus('success');
      
      setTimeout(() => {
        setSaveStatus('idle');
      }, 3000);
    } catch (error) {
      setSaveStatus('error');
    } finally {
      setIsSaving(false);
    }
  };

  const SettingSection = ({ title, icon: Icon, children }: any) => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="card p-6"
    >
      <div className="flex items-center space-x-3 mb-6">
        <div className="p-2 bg-primary-100 dark:bg-primary-900 rounded-lg">
          <Icon className="w-5 h-5 text-primary-600 dark:text-primary-400" />
        </div>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          {title}
        </h3>
      </div>
      <div className="space-y-4">
        {children}
      </div>
    </motion.div>
  );

  const SettingItem = ({ label, description, children }: any) => (
    <div className="flex items-center justify-between py-3 border-b border-gray-200 dark:border-dark-700 last:border-b-0">
      <div className="flex-1">
        <p className="text-sm font-medium text-gray-900 dark:text-white">
          {label}
        </p>
        {description && (
          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
            {description}
          </p>
        )}
      </div>
      <div className="ml-4">
        {children}
      </div>
    </div>
  );

  const Toggle = ({ checked, onChange }: { checked: boolean; onChange: (checked: boolean) => void }) => (
    <button
      onClick={() => onChange(!checked)}
      className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
        checked ? 'bg-primary-600' : 'bg-gray-200 dark:bg-gray-700'
      }`}
    >
      <span
        className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
          checked ? 'translate-x-6' : 'translate-x-1'
        }`}
      />
    </button>
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex justify-between items-center"
      >
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">系统设置</h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            配置系统参数和功能选项
          </p>
        </div>
        
        <button
          onClick={handleSave}
          disabled={isSaving}
          className={`btn-primary flex items-center space-x-2 ${
            isSaving ? 'opacity-50 cursor-not-allowed' : ''
          }`}
        >
          {isSaving ? (
            <>
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
              <span>保存中...</span>
            </>
          ) : (
            <>
              <CheckCircleIcon className="w-5 h-5" />
              <span>保存设置</span>
            </>
          )}
        </button>
      </motion.div>

      {/* Save Status */}
      {saveStatus === 'success' && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg"
        >
          <div className="flex items-center space-x-2 text-green-700 dark:text-green-400">
            <CheckCircleIcon className="w-5 h-5" />
            <span>设置已成功保存</span>
          </div>
        </motion.div>
      )}

      {saveStatus === 'error' && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg"
        >
          <div className="flex items-center space-x-2 text-red-700 dark:text-red-400">
            <ExclamationTriangleIcon className="w-5 h-5" />
            <span>保存失败，请重试</span>
          </div>
        </motion.div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 识别设置 */}
        <SettingSection title="识别设置" icon={UserIcon}>
          <SettingItem
            label="识别阈值"
            description="设置人脸识别的最低置信度阈值"
          >
            <div className="flex items-center space-x-2">
              <input
                type="range"
                min="50"
                max="99"
                value={settings.recognitionThreshold}
                onChange={(e) => handleSettingChange('recognitionThreshold', parseInt(e.target.value))}
                className="w-20"
              />
              <span className="text-sm font-medium text-gray-900 dark:text-white w-8">
                {settings.recognitionThreshold}%
              </span>
            </div>
          </SettingItem>
          
          <SettingItem
            label="活体检测"
            description="启用活体检测以防止照片欺骗"
          >
            <Toggle
              checked={settings.enableLivenessDetection}
              onChange={(checked) => handleSettingChange('enableLivenessDetection', checked)}
            />
          </SettingItem>
          
          <SettingItem
            label="最大人脸尺寸"
            description="设置可识别的最大人脸像素尺寸"
          >
            <input
              type="number"
              value={settings.maxFaceSize}
              onChange={(e) => handleSettingChange('maxFaceSize', parseInt(e.target.value))}
              className="input-field w-20 text-center"
              min="100"
              max="1000"
            />
          </SettingItem>
        </SettingSection>

        {/* 摄像头设置 */}
        <SettingSection title="摄像头设置" icon={CameraIcon}>
          <SettingItem
            label="分辨率"
            description="设置摄像头捕获分辨率"
          >
            <select
              value={settings.cameraResolution}
              onChange={(e) => handleSettingChange('cameraResolution', e.target.value)}
              className="input-field"
            >
              <option value="640x480">640x480</option>
              <option value="1280x720">1280x720</option>
              <option value="1920x1080">1920x1080</option>
            </select>
          </SettingItem>
          
          <SettingItem
            label="帧率"
            description="设置视频捕获帧率"
          >
            <select
              value={settings.frameRate}
              onChange={(e) => handleSettingChange('frameRate', parseInt(e.target.value))}
              className="input-field"
            >
              <option value={15}>15 FPS</option>
              <option value={30}>30 FPS</option>
              <option value={60}>60 FPS</option>
            </select>
          </SettingItem>
          
          <SettingItem
            label="自动对焦"
            description="启用摄像头自动对焦功能"
          >
            <Toggle
              checked={settings.enableAutoFocus}
              onChange={(checked) => handleSettingChange('enableAutoFocus', checked)}
            />
          </SettingItem>
        </SettingSection>

        {/* 安全设置 */}
        <SettingSection title="安全设置" icon={ShieldCheckIcon}>
          <SettingItem
            label="数据加密"
            description="启用人脸数据加密存储"
          >
            <Toggle
              checked={settings.enableEncryption}
              onChange={(checked) => handleSettingChange('enableEncryption', checked)}
            />
          </SettingItem>
          
          <SettingItem
            label="会话超时"
            description="用户会话自动超时时间（分钟）"
          >
            <input
              type="number"
              value={settings.sessionTimeout}
              onChange={(e) => handleSettingChange('sessionTimeout', parseInt(e.target.value))}
              className="input-field w-20 text-center"
              min="5"
              max="120"
            />
          </SettingItem>
          
          <SettingItem
            label="双因素认证"
            description="启用双因素身份验证"
          >
            <Toggle
              checked={settings.enableTwoFactor}
              onChange={(checked) => handleSettingChange('enableTwoFactor', checked)}
            />
          </SettingItem>
        </SettingSection>

        {/* 通知设置 */}
        <SettingSection title="通知设置" icon={BellIcon}>
          <SettingItem
            label="邮件通知"
            description="启用邮件通知功能"
          >
            <Toggle
              checked={settings.enableEmailNotifications}
              onChange={(checked) => handleSettingChange('enableEmailNotifications', checked)}
            />
          </SettingItem>
          
          <SettingItem
            label="识别失败通知"
            description="识别失败时发送通知"
          >
            <Toggle
              checked={settings.notifyOnFailedRecognition}
              onChange={(checked) => handleSettingChange('notifyOnFailedRecognition', checked)}
            />
          </SettingItem>
          
          <SettingItem
            label="新用户注册通知"
            description="有新用户注册时发送通知"
          >
            <Toggle
              checked={settings.notifyOnNewRegistration}
              onChange={(checked) => handleSettingChange('notifyOnNewRegistration', checked)}
            />
          </SettingItem>
        </SettingSection>
      </div>

      {/* 数据库设置 */}
      <SettingSection title="数据库设置" icon={DatabaseIcon}>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <SettingItem
            label="备份频率"
            description="自动备份数据库的频率"
          >
            <select
              value={settings.backupFrequency}
              onChange={(e) => handleSettingChange('backupFrequency', e.target.value)}
              className="input-field"
            >
              <option value="hourly">每小时</option>
              <option value="daily">每天</option>
              <option value="weekly">每周</option>
            </select>
          </SettingItem>
          
          <SettingItem
            label="数据保留期"
            description="识别记录保留天数"
          >
            <input
              type="number"
              value={settings.retentionPeriod}
              onChange={(e) => handleSettingChange('retentionPeriod', parseInt(e.target.value))}
              className="input-field text-center"
              min="30"
              max="365"
            />
          </SettingItem>
          
          <SettingItem
            label="自动清理"
            description="自动清理过期数据"
          >
            <Toggle
              checked={settings.enableAutoCleanup}
              onChange={(checked) => handleSettingChange('enableAutoCleanup', checked)}
            />
          </SettingItem>
        </div>
      </SettingSection>
    </div>
  );
};

export default Settings;
