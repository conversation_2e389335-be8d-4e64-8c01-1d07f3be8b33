from fastapi import FastAPI, File, UploadFile, HTTPException
from fastapi.middleware.cors import CORSMiddleware
import cv2
import numpy as np
import sqlite3
import json
import base64
from datetime import datetime
from typing import List, Optional
from pydantic import BaseModel
import os
import uuid

app = FastAPI(title="智能人脸识别系统 API (简化版)", version="1.0.0")

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 数据模型
class UserCreate(BaseModel):
    name: str
    email: Optional[str] = None
    department: Optional[str] = None
    position: Optional[str] = None

class UserResponse(BaseModel):
    id: str
    name: str
    email: Optional[str]
    department: Optional[str]
    position: Optional[str]
    registered_at: datetime
    status: str

class RecognitionResult(BaseModel):
    user_id: Optional[str]
    name: Optional[str]
    confidence: float
    timestamp: datetime
    status: str

# 初始化人脸检测器
face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')

# 数据库初始化
def init_database():
    conn = sqlite3.connect('face_recognition.db')
    cursor = conn.cursor()
    
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS users (
            id TEXT PRIMARY KEY,
            name TEXT NOT NULL,
            email TEXT,
            department TEXT,
            position TEXT,
            face_data TEXT NOT NULL,
            registered_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            status TEXT DEFAULT 'active'
        )
    ''')
    
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS recognition_logs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id TEXT,
            confidence REAL,
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            status TEXT,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
    ''')
    
    conn.commit()
    conn.close()

init_database()

def detect_face_opencv(image_data: bytes):
    """使用OpenCV检测人脸"""
    nparr = np.frombuffer(image_data, np.uint8)
    image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    
    faces = face_cascade.detectMultiScale(gray, 1.1, 4)
    
    if len(faces) == 0:
        raise HTTPException(status_code=400, detail="未检测到人脸")
    
    if len(faces) > 1:
        raise HTTPException(status_code=400, detail="检测到多张人脸，请确保图像中只有一张人脸")
    
    # 提取人脸区域
    (x, y, w, h) = faces[0]
    face_roi = gray[y:y+h, x:x+w]
    
    # 调整大小并转换为特征向量（简化版）
    face_resized = cv2.resize(face_roi, (100, 100))
    face_vector = face_resized.flatten().tolist()
    
    return face_vector

@app.get("/")
async def root():
    return {"message": "智能人脸识别系统 API (简化版)"}

@app.post("/api/users/register", response_model=UserResponse)
async def register_user(
    user_data: UserCreate,
    face_image: UploadFile = File(...)
):
    """注册新用户"""
    try:
        image_data = await face_image.read()
        face_vector = detect_face_opencv(image_data)
        
        user_id = str(uuid.uuid4())
        
        conn = sqlite3.connect('face_recognition.db')
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO users (id, name, email, department, position, face_data)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (
            user_id,
            user_data.name,
            user_data.email,
            user_data.department,
            user_data.position,
            json.dumps(face_vector)
        ))
        
        conn.commit()
        conn.close()
        
        return UserResponse(
            id=user_id,
            name=user_data.name,
            email=user_data.email,
            department=user_data.department,
            position=user_data.position,
            registered_at=datetime.now(),
            status="active"
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"注册失败: {str(e)}")

@app.post("/api/recognition/identify", response_model=RecognitionResult)
async def identify_face(face_image: UploadFile = File(...)):
    """识别人脸（简化版 - 随机返回结果用于演示）"""
    try:
        image_data = await face_image.read()
        face_vector = detect_face_opencv(image_data)
        
        # 获取所有用户
        conn = sqlite3.connect('face_recognition.db')
        cursor = conn.cursor()
        cursor.execute("SELECT id, name FROM users WHERE status = 'active'")
        users = cursor.fetchall()
        
        if not users:
            cursor.execute('''
                INSERT INTO recognition_logs (user_id, confidence, status)
                VALUES (?, ?, ?)
            ''', (None, 0.0, 'unknown'))
            conn.commit()
            conn.close()
            
            return RecognitionResult(
                user_id=None,
                name="未知用户",
                confidence=0.0,
                timestamp=datetime.now(),
                status="unknown"
            )
        
        # 简化版：随机选择一个用户并给出置信度
        import random
        if random.random() > 0.3:  # 70%概率识别成功
            user = random.choice(users)
            confidence = random.uniform(75.0, 95.0)
            status = "success"
            user_id, name = user[0], user[1]
        else:
            confidence = random.uniform(30.0, 60.0)
            status = "failed"
            user_id, name = None, "未知用户"
        
        # 记录识别结果
        cursor.execute('''
            INSERT INTO recognition_logs (user_id, confidence, status)
            VALUES (?, ?, ?)
        ''', (user_id, confidence, status))
        conn.commit()
        conn.close()
        
        return RecognitionResult(
            user_id=user_id,
            name=name,
            confidence=confidence,
            timestamp=datetime.now(),
            status=status
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"识别失败: {str(e)}")

@app.get("/api/users", response_model=List[UserResponse])
async def get_users():
    """获取所有用户"""
    conn = sqlite3.connect('face_recognition.db')
    cursor = conn.cursor()
    
    cursor.execute('''
        SELECT id, name, email, department, position, registered_at, status
        FROM users
        ORDER BY registered_at DESC
    ''')
    
    users = cursor.fetchall()
    conn.close()
    
    return [
        UserResponse(
            id=user[0],
            name=user[1],
            email=user[2],
            department=user[3],
            position=user[4],
            registered_at=datetime.fromisoformat(user[5]),
            status=user[6]
        )
        for user in users
    ]

@app.get("/api/analytics/stats")
async def get_analytics_stats():
    """获取分析统计数据"""
    conn = sqlite3.connect('face_recognition.db')
    cursor = conn.cursor()
    
    cursor.execute("SELECT COUNT(*) FROM users WHERE status = 'active'")
    total_users = cursor.fetchone()[0]
    
    cursor.execute('''
        SELECT COUNT(*) FROM recognition_logs 
        WHERE DATE(timestamp) = DATE('now')
    ''')
    today_recognitions = cursor.fetchone()[0]
    
    cursor.execute('''
        SELECT 
            COUNT(CASE WHEN status = 'success' THEN 1 END) * 100.0 / COUNT(*) as success_rate
        FROM recognition_logs 
        WHERE DATE(timestamp) >= DATE('now', '-7 days')
    ''')
    result = cursor.fetchone()
    success_rate = result[0] if result[0] is not None else 0
    
    conn.close()
    
    return {
        "total_users": total_users,
        "today_recognitions": today_recognitions,
        "success_rate": round(success_rate, 1),
        "active_users": total_users
    }

@app.delete("/api/users/{user_id}")
async def delete_user(user_id: str):
    """删除用户"""
    conn = sqlite3.connect('face_recognition.db')
    cursor = conn.cursor()
    
    cursor.execute("UPDATE users SET status = 'deleted' WHERE id = ?", (user_id,))
    
    if cursor.rowcount == 0:
        conn.close()
        raise HTTPException(status_code=404, detail="用户不存在")
    
    conn.commit()
    conn.close()
    
    return {"message": "用户已删除"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
