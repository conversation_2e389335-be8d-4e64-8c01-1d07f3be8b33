{"version": 3, "sources": ["webpack://Webcam/webpack/universalModuleDefinition", "webpack://Webcam/webpack/bootstrap", "webpack://Webcam/src/react-webcam.tsx", "webpack://Webcam/external%20%7B%22root%22:%22React%22,%22commonjs2%22:%22react%22,%22commonjs%22:%22react%22,%22amd%22:%22react%22%7D"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(require(\"react\"));\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([\"react\"], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"Webcam\"] = factory(require(\"react\"));\n\telse\n\t\troot[\"Webcam\"] = factory(root[\"React\"]);\n})(this, function(__WEBPACK_EXTERNAL_MODULE_react__) {\nreturn ", " \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = \"./src/react-webcam.tsx\");\n", "import * as React from \"react\";\n\n// polyfill based on https://developer.mozilla.org/en-US/docs/Web/API/MediaDevices/getUserMedia\n(function polyfillGetUserMedia() {\n  if (typeof window === 'undefined') {\n    return;\n  }\n\n  // Older browsers might not implement mediaDevices at all, so we set an empty object first\n  if (navigator.mediaDevices === undefined) {\n    (navigator as any).mediaDevices = {};\n  }\n\n  // Some browsers partially implement mediaDevices. We can't just assign an object\n  // with getUserMedia as it would overwrite existing properties.\n  // Here, we will just add the getUserMedia property if it's missing.\n  if (navigator.mediaDevices.getUserMedia === undefined) {\n    navigator.mediaDevices.getUserMedia = function(constraints) {\n      // First get ahold of the legacy getUserMedia, if present\n      const getUserMedia =\n        navigator.getUserMedia ||\n        navigator.webkitGetUserMedia ||\n        navigator.mozGetUserMedia ||\n        navigator.msGetUserMedia;\n\n      // Some browsers just don't implement it - return a rejected promise with an error\n      // to keep a consistent interface\n      if (!getUserMedia) {\n        return Promise.reject(\n          new Error(\"getUserMedia is not implemented in this browser\")\n        );\n      }\n\n      // Otherwise, wrap the call to the old navigator.getUserMedia with a Promise\n      return new Promise(function(resolve, reject) {\n        getUserMedia.call(navigator, constraints, resolve, reject);\n      });\n    };\n  }\n})();\n\nfunction hasGetUserMedia() {\n  return !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia);\n}\n\ninterface ScreenshotDimensions {\n  width: number;\n  height: number;\n}\n\ninterface ChildrenProps {\n  getScreenshot: (screenshotDimensions?: ScreenshotDimensions) => string | null;\n}\n\nexport type WebcamProps = Omit<React.HTMLProps<HTMLVideoElement>, \"ref\"> & {\n  audio: boolean;\n  audioConstraints?: MediaStreamConstraints[\"audio\"];\n  disablePictureInPicture: boolean;\n  forceScreenshotSourceSize: boolean;\n  imageSmoothing: boolean;\n  mirrored: boolean;\n  minScreenshotHeight?: number;\n  minScreenshotWidth?: number;\n  onUserMedia: (stream: MediaStream) => void;\n  onUserMediaError: (error: string | DOMException) => void;\n  screenshotFormat: \"image/webp\" | \"image/png\" | \"image/jpeg\";\n  screenshotQuality: number;\n  videoConstraints?: MediaStreamConstraints[\"video\"];\n  children?: (childrenProps: ChildrenProps) => JSX.Element;\n}\n\ninterface WebcamState {\n  hasUserMedia: boolean;\n  src?: string;\n}\n\nexport default class Webcam extends React.Component<WebcamProps, WebcamState> {\n  static defaultProps = {\n    audio: false,\n    disablePictureInPicture: false,\n    forceScreenshotSourceSize: false,\n    imageSmoothing: true,\n    mirrored: false,\n    onUserMedia: () => undefined,\n    onUserMediaError: () => undefined,\n    screenshotFormat: \"image/webp\",\n    screenshotQuality: 0.92,\n  };\n\n  private canvas: HTMLCanvasElement | null = null;\n\n  private ctx: CanvasRenderingContext2D | null = null;\n\n  private requestUserMediaId = 0;\n\n  private unmounted = false;\n\n  stream: MediaStream | null;\n\n  video: HTMLVideoElement | null;\n\n  constructor(props: WebcamProps) {\n    super(props);\n    this.state = {\n      hasUserMedia: false\n    };\n  }\n\n  componentDidMount() {\n    const { state, props } = this;\n    this.unmounted = false;\n\n    if (!hasGetUserMedia()) {\n      props.onUserMediaError(\"getUserMedia not supported\");\n\n      return;\n    }\n\n    if (!state.hasUserMedia) {\n      this.requestUserMedia();\n    }\n\n    if (props.children && typeof props.children != 'function') {\n      console.warn(\"children must be a function\");\n    }\n  }\n\n  componentDidUpdate(nextProps: WebcamProps) {\n    const { props } = this;\n\n    if (!hasGetUserMedia()) {\n      props.onUserMediaError(\"getUserMedia not supported\");\n\n      return;\n    }\n\n    const audioConstraintsChanged =\n      JSON.stringify(nextProps.audioConstraints) !==\n      JSON.stringify(props.audioConstraints);\n    const videoConstraintsChanged =\n      JSON.stringify(nextProps.videoConstraints) !==\n      JSON.stringify(props.videoConstraints);\n    const minScreenshotWidthChanged =\n      nextProps.minScreenshotWidth !== props.minScreenshotWidth;\n    const minScreenshotHeightChanged =\n      nextProps.minScreenshotHeight !== props.minScreenshotHeight;\n    if (\n      videoConstraintsChanged ||\n      minScreenshotWidthChanged ||\n      minScreenshotHeightChanged\n    ) {\n      this.canvas = null;\n      this.ctx = null;\n    }\n    if (audioConstraintsChanged || videoConstraintsChanged) {\n      this.stopAndCleanup();\n      this.requestUserMedia();\n    }\n  }\n\n  componentWillUnmount() {\n    this.unmounted = true;\n    this.stopAndCleanup();\n  }\n\n  private static stopMediaStream(stream: MediaStream | null) {\n    if (stream) {\n      if (stream.getVideoTracks && stream.getAudioTracks) {\n        stream.getVideoTracks().map(track => {\n          stream.removeTrack(track);\n          track.stop();\n        });\n        stream.getAudioTracks().map(track => {\n          stream.removeTrack(track);\n          track.stop()\n        });\n      } else {\n        ((stream as unknown) as MediaStreamTrack).stop();\n      }\n    }\n  }\n\n  private stopAndCleanup() {\n    const { state } = this;\n\n    if (state.hasUserMedia) {\n      Webcam.stopMediaStream(this.stream);\n\n      if (state.src) {\n        window.URL.revokeObjectURL(state.src);\n      }\n    }\n  }\n\n  getScreenshot(screenshotDimensions?: ScreenshotDimensions) {\n    const { state, props } = this;\n\n    if (!state.hasUserMedia) return null;\n\n    const canvas = this.getCanvas(screenshotDimensions);\n    return (\n      canvas &&\n      canvas.toDataURL(props.screenshotFormat, props.screenshotQuality)\n    );\n  }\n\n  getCanvas(screenshotDimensions?: ScreenshotDimensions) {\n    const { state, props } = this;\n\n    if (!this.video) {\n      return null;\n    }\n\n    if (!state.hasUserMedia || !this.video.videoHeight) return null;\n\n    if (!this.ctx) {\n      let canvasWidth = this.video.videoWidth;\n      let canvasHeight = this.video.videoHeight;\n      if (!this.props.forceScreenshotSourceSize) {\n        const aspectRatio = canvasWidth / canvasHeight;\n\n        canvasWidth = props.minScreenshotWidth || this.video.clientWidth;\n        canvasHeight = canvasWidth / aspectRatio;\n\n        if (\n          props.minScreenshotHeight &&\n          canvasHeight < props.minScreenshotHeight\n        ) {\n          canvasHeight = props.minScreenshotHeight;\n          canvasWidth = canvasHeight * aspectRatio;\n        }\n      }\n\n      this.canvas = document.createElement(\"canvas\");\n      this.canvas.width = screenshotDimensions?.width ||  canvasWidth;\n      this.canvas.height = screenshotDimensions?.height || canvasHeight;\n      this.ctx = this.canvas.getContext(\"2d\");\n    }\n\n    const { ctx, canvas } = this;\n\n    if (ctx && canvas) {\n\n      // adjust the height and width of the canvas to the given dimensions\n      canvas.width = screenshotDimensions?.width ||  canvas.width;\n      canvas.height = screenshotDimensions?.height || canvas.height;\n\n      // mirror the screenshot\n      if (props.mirrored) {\n        ctx.translate(canvas.width, 0);\n        ctx.scale(-1, 1);\n      }\n\n      ctx.imageSmoothingEnabled = props.imageSmoothing;\n      ctx.drawImage(this.video, 0, 0, screenshotDimensions?.width || canvas.width, screenshotDimensions?.height || canvas.height);\n\n      // invert mirroring\n      if (props.mirrored) {\n        ctx.scale(-1, 1);\n        ctx.translate(-canvas.width, 0);\n      }\n    }\n\n    return canvas;\n  }\n\n  private requestUserMedia() {\n    const { props } = this;\n\n    const sourceSelected = (\n      audioConstraints: boolean | MediaTrackConstraints | undefined,\n      videoConstraints: boolean | MediaTrackConstraints | undefined,\n    ) => {\n      const constraints: MediaStreamConstraints = {\n        video: typeof videoConstraints !== \"undefined\" ? videoConstraints : true\n      };\n\n      if (props.audio) {\n        constraints.audio =\n          typeof audioConstraints !== \"undefined\" ? audioConstraints : true;\n      }\n\n      this.requestUserMediaId++\n      const myRequestUserMediaId = this.requestUserMediaId\n\n      navigator.mediaDevices\n        .getUserMedia(constraints)\n        .then(stream => {\n          if (this.unmounted || myRequestUserMediaId !== this.requestUserMediaId) {\n            Webcam.stopMediaStream(stream);\n          } else {\n            this.handleUserMedia(null, stream);\n          }\n        })\n        .catch(e => {\n          this.handleUserMedia(e);\n        });\n    };\n\n    if (\"mediaDevices\" in navigator) {\n      sourceSelected(props.audioConstraints, props.videoConstraints);\n    } else {\n      const optionalSource = (id: string | null) => ({ optional: [{ sourceId: id }] }) as MediaTrackConstraints;\n\n      const constraintToSourceId = (constraint) => {\n        const { deviceId } = constraint;\n\n        if (typeof deviceId === \"string\") {\n          return deviceId;\n        }\n\n        if (Array.isArray(deviceId) && deviceId.length > 0) {\n          return deviceId[0];\n        }\n\n        if (typeof deviceId === \"object\" && deviceId.ideal) {\n          return deviceId.ideal;\n        }\n\n        return null;\n      };\n\n      // @ts-ignore: deprecated api\n      MediaStreamTrack.getSources(sources => {\n        let audioSource: string | null = null;\n        let videoSource: string | null = null;\n\n        sources.forEach((source: MediaStreamTrack) => {\n          if (source.kind === \"audio\") {\n            audioSource = source.id;\n          } else if (source.kind === \"video\") {\n            videoSource = source.id;\n          }\n        });\n\n        const audioSourceId = constraintToSourceId(props.audioConstraints);\n        if (audioSourceId) {\n          audioSource = audioSourceId;\n        }\n\n        const videoSourceId = constraintToSourceId(props.videoConstraints);\n        if (videoSourceId) {\n          videoSource = videoSourceId;\n        }\n\n        sourceSelected(\n          optionalSource(audioSource),\n          optionalSource(videoSource)\n        );\n      });\n    }\n  }\n\n  private handleUserMedia(err, stream?: MediaStream) {\n    const { props } = this;\n\n    if (err || !stream) {\n      this.setState({ hasUserMedia: false });\n      props.onUserMediaError(err);\n\n      return;\n    }\n\n    this.stream = stream;\n\n    try {\n      if (this.video) {\n        this.video.srcObject = stream;\n      }\n      this.setState({ hasUserMedia: true });\n    } catch (error) {\n      this.setState({\n        hasUserMedia: true,\n        src: window.URL.createObjectURL(stream)\n      });\n    }\n\n    props.onUserMedia(stream);\n  }\n\n  render() {\n    const { state, props } = this;\n\n    const {\n      audio,\n      forceScreenshotSourceSize,\n      disablePictureInPicture,\n      onUserMedia,\n      onUserMediaError,\n      screenshotFormat,\n      screenshotQuality,\n      minScreenshotWidth,\n      minScreenshotHeight,\n      audioConstraints,\n      videoConstraints,\n      imageSmoothing,\n      mirrored,\n      style = {},\n      children,\n      ...rest\n    } = props;\n\n    const videoStyle = mirrored ? { ...style, transform: `${style.transform || \"\"} scaleX(-1)` } : style;\n\n    const childrenProps: ChildrenProps = {\n      getScreenshot: this.getScreenshot.bind(this),\n    };\n\n    return (\n      <>\n        <video\n          autoPlay\n          disablePictureInPicture={disablePictureInPicture}\n          src={state.src}\n          muted={!audio}\n          playsInline\n          ref={ref => {\n            this.video = ref;\n          }}\n          style={videoStyle}\n          {...rest}\n        />\n        {children && children(childrenProps)}\n      </>\n    );\n  }\n}\n", "module.exports = __WEBPACK_EXTERNAL_MODULE_react__;"], "mappings": ";;;;;;;;AAAA;;KAAA,SAAA,iCAAA,MAAA,SAAA;AACA,UAAA,OAAA,YAAA,YAAA,OAAA,WAAA;AACA,eAAA,UAAA,QAAA,eAAA;eACA,OAAA,WAAA,cAAA,OAAA;AACA,eAAA,CAAA,OAAA,GAAA,OAAA;eACA,OAAA,YAAA;AACA,gBAAA,QAAA,IAAA,QAAA,eAAA;;AAEA,aAAA,QAAA,IAAA,QAAA,KAAA,OAAA,CAAA;IACA,GAAC,SAAA,SAAA,mCAAA;AACD;;QAAA,SAAA,SAAA;ACTA,cAAA,mBAAA,CAAA;AAGA,mBAAA,oBAAA,UAAA;AAGA,gBAAA,iBAAA,QAAA,GAAA;AACA,qBAAA,iBAAA,QAAA,EAAA;YACA;AAEA,gBAAAA,UAAA,iBAAA,QAAA,IAAA;;cACA,GAAA;;cACA,GAAA;;cACA,SAAA,CAAA;;YACA;AAGA,oBAAA,QAAA,EAAA,KAAAA,QAAA,SAAAA,SAAAA,QAAA,SAAA,mBAAA;AAGA,YAAAA,QAAA,IAAA;AAGA,mBAAAA,QAAA;UACA;AAIA,8BAAA,IAAA;AAGA,8BAAA,IAAA;AAGA,8BAAA,IAAA,SAAAC,UAAA,MAAA,QAAA;AACA,gBAAA,CAAA,oBAAA,EAAAA,UAAA,IAAA,GAAA;AACA,qBAAA,eAAAA,UAAA,MAAA,EAA0C,YAAA,MAAA,KAAA,OAAA,CAAgC;YAC1E;UACA;AAGA,8BAAA,IAAA,SAAAA,UAAA;AACA,gBAAA,OAAA,WAAA,eAAA,OAAA,aAAA;AACA,qBAAA,eAAAA,UAAA,OAAA,aAAA,EAAwD,OAAA,SAAA,CAAkB;YAC1E;AACA,mBAAA,eAAAA,UAAA,cAAA,EAAiD,OAAA,KAAA,CAAc;UAC/D;AAOA,8BAAA,IAAA,SAAA,OAAA,MAAA;AACA,gBAAA,OAAA,EAAA,SAAA,oBAAA,KAAA;AACA,gBAAA,OAAA,EAAA,QAAA;AACA,gBAAA,OAAA,KAAA,OAAA,UAAA,YAAA,SAAA,MAAA,WAAA,QAAA;AACA,gBAAA,KAAA,uBAAA,OAAA,IAAA;AACA,gCAAA,EAAA,EAAA;AACA,mBAAA,eAAA,IAAA,WAAA,EAAyC,YAAA,MAAA,MAAA,CAAiC;AAC1E,gBAAA,OAAA,KAAA,OAAA,SAAA,SAAA,UAAA,OAAA,MAAA,qBAAA,EAAA,IAAA,MAAA,SAAAC,MAAA;AAAgH,qBAAA,MAAAA,IAAA;YAAmB,GAAE,KAAA,MAAA,GAAA,CAAA;AACrI,mBAAA;UACA;AAGA,8BAAA,IAAA,SAAAF,SAAA;AACA,gBAAA,SAAAA,WAAAA,QAAA;;cACA,SAAA,aAAA;AAA2B,uBAAAA,QAAA,SAAA;cAA0B;;;cACrD,SAAA,mBAAA;AAAiC,uBAAAA;cAAe;;AAChD,gCAAA,EAAA,QAAA,KAAA,MAAA;AACA,mBAAA;UACA;AAGA,8BAAA,IAAA,SAAA,QAAA,UAAA;AAAsD,mBAAA,OAAA,UAAA,eAAA,KAAA,QAAA,QAAA;UAA+D;AAGrH,8BAAA,IAAA;AAIA,iBAAA,oBAAA,oBAAA,IAAA,wBAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC/EA,eAAC,SAAS,uBAAoB;AAC5B,oBAAI,OAAO,WAAW,aAAa;AACjC;;AAIF,oBAAI,UAAU,iBAAiB,QAAW;AACvC,4BAAkB,eAAe,CAAA;;AAMpC,oBAAI,UAAU,aAAa,iBAAiB,QAAW;AACrD,4BAAU,aAAa,eAAe,SAAS,aAAW;AAExD,wBAAM,eACJ,UAAU,gBACV,UAAU,sBACV,UAAU,mBACV,UAAU;AAIZ,wBAAI,CAAC,cAAc;AACjB,6BAAO,QAAQ,OACb,IAAI,MAAM,iDAAiD,CAAC;;AAKhE,2BAAO,IAAI,QAAQ,SAAS,SAAS,QAAM;AACzC,mCAAa,KAAK,WAAW,aAAa,SAAS,MAAM;oBAC3D,CAAC;kBACH;;cAEJ,GAAE;AAEF,uBAAS,kBAAe;AACtB,uBAAO,CAAC,EAAE,UAAU,gBAAgB,UAAU,aAAa;cAC7D;AAiCA,kBAAA;;gBAAA,SAAA,QAAA;AAAoC,4BAAAG,SAAA,MAAA;AAyBlC,2BAAAA,QAAY,OAAkB;AAA9B,wBAAA,QACE,OAAA,KAAA,MAAM,KAAK,KAAC;AAbN,0BAAA,SAAmC;AAEnC,0BAAA,MAAuC;AAEvC,0BAAA,qBAAqB;AAErB,0BAAA,YAAY;AAQlB,0BAAK,QAAQ;sBACX,cAAc;;;kBAElB;AAEA,kBAAAA,QAAA,UAAA,oBAAA,WAAA;AACQ,wBAAA,KAAmB,MAAjB,QAAK,GAAA,OAAE,QAAK,GAAA;AACpB,yBAAK,YAAY;AAEjB,wBAAI,CAAC,gBAAe,GAAI;AACtB,4BAAM,iBAAiB,4BAA4B;AAEnD;;AAGF,wBAAI,CAAC,MAAM,cAAc;AACvB,2BAAK,iBAAgB;;AAGvB,wBAAI,MAAM,YAAY,OAAO,MAAM,YAAY,YAAY;AACzD,8BAAQ,KAAK,6BAA6B;;kBAE9C;AAEA,kBAAAA,QAAA,UAAA,qBAAA,SAAmB,WAAsB;AAC/B,wBAAA,QAAU,KAAI;AAEtB,wBAAI,CAAC,gBAAe,GAAI;AACtB,4BAAM,iBAAiB,4BAA4B;AAEnD;;AAGF,wBAAM,0BACJ,KAAK,UAAU,UAAU,gBAAgB,MACzC,KAAK,UAAU,MAAM,gBAAgB;AACvC,wBAAM,0BACJ,KAAK,UAAU,UAAU,gBAAgB,MACzC,KAAK,UAAU,MAAM,gBAAgB;AACvC,wBAAM,4BACJ,UAAU,uBAAuB,MAAM;AACzC,wBAAM,6BACJ,UAAU,wBAAwB,MAAM;AAC1C,wBACE,2BACA,6BACA,4BACA;AACA,2BAAK,SAAS;AACd,2BAAK,MAAM;;AAEb,wBAAI,2BAA2B,yBAAyB;AACtD,2BAAK,eAAc;AACnB,2BAAK,iBAAgB;;kBAEzB;AAEA,kBAAAA,QAAA,UAAA,uBAAA,WAAA;AACE,yBAAK,YAAY;AACjB,yBAAK,eAAc;kBACrB;AAEe,kBAAAA,QAAA,kBAAf,SAA+B,QAA0B;AACvD,wBAAI,QAAQ;AACV,0BAAI,OAAO,kBAAkB,OAAO,gBAAgB;AAClD,+BAAO,eAAc,EAAG,IAAI,SAAA,OAAK;AAC/B,iCAAO,YAAY,KAAK;AACxB,gCAAM,KAAI;wBACZ,CAAC;AACD,+BAAO,eAAc,EAAG,IAAI,SAAA,OAAK;AAC/B,iCAAO,YAAY,KAAK;AACxB,gCAAM,KAAI;wBACZ,CAAC;6BACI;AACH,+BAAwC,KAAI;;;kBAGpD;AAEQ,kBAAAA,QAAA,UAAA,iBAAR,WAAA;AACU,wBAAA,QAAU,KAAI;AAEtB,wBAAI,MAAM,cAAc;AACtB,sBAAAA,QAAO,gBAAgB,KAAK,MAAM;AAElC,0BAAI,MAAM,KAAK;AACb,+BAAO,IAAI,gBAAgB,MAAM,GAAG;;;kBAG1C;AAEA,kBAAAA,QAAA,UAAA,gBAAA,SAAc,sBAA2C;AACjD,wBAAA,KAAmB,MAAjB,QAAK,GAAA,OAAE,QAAK,GAAA;AAEpB,wBAAI,CAAC,MAAM;AAAc,6BAAO;AAEhC,wBAAM,SAAS,KAAK,UAAU,oBAAoB;AAClD,2BACE,UACA,OAAO,UAAU,MAAM,kBAAkB,MAAM,iBAAiB;kBAEpE;AAEA,kBAAAA,QAAA,UAAA,YAAA,SAAU,sBAA2C;AAC7C,wBAAA,KAAmB,MAAjB,QAAK,GAAA,OAAE,QAAK,GAAA;AAEpB,wBAAI,CAAC,KAAK,OAAO;AACf,6BAAO;;AAGT,wBAAI,CAAC,MAAM,gBAAgB,CAAC,KAAK,MAAM;AAAa,6BAAO;AAE3D,wBAAI,CAAC,KAAK,KAAK;AACb,0BAAI,cAAc,KAAK,MAAM;AAC7B,0BAAI,eAAe,KAAK,MAAM;AAC9B,0BAAI,CAAC,KAAK,MAAM,2BAA2B;AACzC,4BAAM,cAAc,cAAc;AAElC,sCAAc,MAAM,sBAAsB,KAAK,MAAM;AACrD,uCAAe,cAAc;AAE7B,4BACE,MAAM,uBACN,eAAe,MAAM,qBACrB;AACA,yCAAe,MAAM;AACrB,wCAAc,eAAe;;;AAIjC,2BAAK,SAAS,SAAS,cAAc,QAAQ;AAC7C,2BAAK,OAAO,SAAQ,yBAAoB,QAApB,yBAAoB,SAAA,SAApB,qBAAsB,UAAU;AACpD,2BAAK,OAAO,UAAS,yBAAoB,QAApB,yBAAoB,SAAA,SAApB,qBAAsB,WAAU;AACrD,2BAAK,MAAM,KAAK,OAAO,WAAW,IAAI;;AAGlC,wBAAA,KAAkB,MAAhB,MAAG,GAAA,KAAE,SAAM,GAAA;AAEnB,wBAAI,OAAO,QAAQ;AAGjB,6BAAO,SAAQ,yBAAoB,QAApB,yBAAoB,SAAA,SAApB,qBAAsB,UAAU,OAAO;AACtD,6BAAO,UAAS,yBAAoB,QAApB,yBAAoB,SAAA,SAApB,qBAAsB,WAAU,OAAO;AAGvD,0BAAI,MAAM,UAAU;AAClB,4BAAI,UAAU,OAAO,OAAO,CAAC;AAC7B,4BAAI,MAAM,IAAI,CAAC;;AAGjB,0BAAI,wBAAwB,MAAM;AAClC,0BAAI,UAAU,KAAK,OAAO,GAAG,IAAG,yBAAoB,QAApB,yBAAoB,SAAA,SAApB,qBAAsB,UAAS,OAAO,QAAO,yBAAoB,QAApB,yBAAoB,SAAA,SAApB,qBAAsB,WAAU,OAAO,MAAM;AAG1H,0BAAI,MAAM,UAAU;AAClB,4BAAI,MAAM,IAAI,CAAC;AACf,4BAAI,UAAU,CAAC,OAAO,OAAO,CAAC;;;AAIlC,2BAAO;kBACT;AAEQ,kBAAAA,QAAA,UAAA,mBAAR,WAAA;AAAA,wBAAA,QAAA;AACU,wBAAA,QAAU,KAAI;AAEtB,wBAAM,iBAAiB,SACrB,kBACA,kBAA6D;AAE7D,0BAAM,cAAsC;wBAC1C,OAAO,OAAO,qBAAqB,cAAc,mBAAmB;;AAGtE,0BAAI,MAAM,OAAO;AACf,oCAAY,QACV,OAAO,qBAAqB,cAAc,mBAAmB;;AAGjE,4BAAK;AACL,0BAAM,uBAAuB,MAAK;AAElC,gCAAU,aACP,aAAa,WAAW,EACxB,KAAK,SAAA,QAAM;AACV,4BAAI,MAAK,aAAa,yBAAyB,MAAK,oBAAoB;AACtE,0BAAAA,QAAO,gBAAgB,MAAM;+BACxB;AACL,gCAAK,gBAAgB,MAAM,MAAM;;sBAErC,CAAC,EACA,MAAM,SAAA,GAAC;AACN,8BAAK,gBAAgB,CAAC;sBACxB,CAAC;oBACL;AAEA,wBAAI,kBAAkB,WAAW;AAC/B,qCAAe,MAAM,kBAAkB,MAAM,gBAAgB;2BACxD;AACL,0BAAM,mBAAiB,SAAC,IAAiB;AAAK,+BAAC,EAAE,UAAU,CAAC,EAAE,UAAU,GAAE,CAAE,EAAC;sBAA/B;AAE9C,0BAAM,yBAAuB,SAAC,YAAU;AAC9B,4BAAA,WAAa,WAAU;AAE/B,4BAAI,OAAO,aAAa,UAAU;AAChC,iCAAO;;AAGT,4BAAI,MAAM,QAAQ,QAAQ,KAAK,SAAS,SAAS,GAAG;AAClD,iCAAO,SAAS,CAAC;;AAGnB,4BAAI,OAAO,aAAa,YAAY,SAAS,OAAO;AAClD,iCAAO,SAAS;;AAGlB,+BAAO;sBACT;AAGA,uCAAiB,WAAW,SAAA,SAAO;AACjC,4BAAI,cAA6B;AACjC,4BAAI,cAA6B;AAEjC,gCAAQ,QAAQ,SAAC,QAAwB;AACvC,8BAAI,OAAO,SAAS,SAAS;AAC3B,0CAAc,OAAO;qCACZ,OAAO,SAAS,SAAS;AAClC,0CAAc,OAAO;;wBAEzB,CAAC;AAED,4BAAM,gBAAgB,uBAAqB,MAAM,gBAAgB;AACjE,4BAAI,eAAe;AACjB,wCAAc;;AAGhB,4BAAM,gBAAgB,uBAAqB,MAAM,gBAAgB;AACjE,4BAAI,eAAe;AACjB,wCAAc;;AAGhB,uCACE,iBAAe,WAAW,GAC1B,iBAAe,WAAW,CAAC;sBAE/B,CAAC;;kBAEL;AAEQ,kBAAAA,QAAA,UAAA,kBAAR,SAAwB,KAAK,QAAoB;AACvC,wBAAA,QAAU,KAAI;AAEtB,wBAAI,OAAO,CAAC,QAAQ;AAClB,2BAAK,SAAS,EAAE,cAAc,MAAK,CAAE;AACrC,4BAAM,iBAAiB,GAAG;AAE1B;;AAGF,yBAAK,SAAS;AAEd,wBAAI;AACF,0BAAI,KAAK,OAAO;AACd,6BAAK,MAAM,YAAY;;AAEzB,2BAAK,SAAS,EAAE,cAAc,KAAI,CAAE;6BAC7B,OAAO;AACd,2BAAK,SAAS;wBACZ,cAAc;wBACd,KAAK,OAAO,IAAI,gBAAgB,MAAM;uBACvC;;AAGH,0BAAM,YAAY,MAAM;kBAC1B;AAEA,kBAAAA,QAAA,UAAA,SAAA,WAAA;AAAA,wBAAA,QAAA;AACQ,wBAAA,KAAmB,MAAjB,QAAK,GAAA,OAAE,QAAK,GAAA;AAGlB,wBAAA,QAgBE,MAAK,OAfP,4BAeE,MAAK,2BAdP,0BAcE,MAAK,yBAbP,cAaE,MAAK,aAZP,mBAYE,MAAK,kBAXP,mBAWE,MAAK,kBAVP,oBAUE,MAAK,mBATP,qBASE,MAAK,oBARP,sBAQE,MAAK,qBAPP,mBAOE,MAAK,kBANP,mBAME,MAAK,kBALP,iBAKE,MAAK,gBAJP,WAIE,MAAK,UAHP,KAGE,MAAK,OAHP,QAAK,OAAA,SAAG,CAAA,IAAE,IACV,WAEE,MAAK,UADJ,OAAI,OACL,OAjBE,CAAA,SAAA,6BAAA,2BAAA,eAAA,oBAAA,oBAAA,qBAAA,sBAAA,uBAAA,oBAAA,oBAAA,kBAAA,YAAA,SAAA,UAAA,CAiBL;AAED,wBAAM,aAAa,WAAU,SAAA,SAAA,CAAA,GAAM,KAAK,GAAA,EAAE,YAAc,MAAM,aAAa,MAAE,cAAa,CAAA,IAAK;AAE/F,wBAAM,gBAA+B;sBACnC,eAAe,KAAK,cAAc,KAAK,IAAI;;AAG7C,2BACE,mCAAA,eAAA;sBAAA,mCAAA,UAAA;sBAAA;sBACE,mCAAA,eAAA,EAAA,SAAA,SAAA,EACE,UAAQ,MACR,yBACA,KAAK,MAAM,KACX,OAAO,CAAC,OACR,aAAW,MACX,KAAK,SAAA,KAAG;AACN,8BAAK,QAAQ;sBACf,GACA,OAAO,WAAU,GACb,IAAI,CAAA;sBAET,YAAY,SAAS,aAAa;oBAAC;kBAG1C;AA5VO,kBAAAA,QAAA,eAAe;oBACpB,OAAO;oBACP,yBAAyB;oBACzB,2BAA2B;oBAC3B,gBAAgB;oBAChB,UAAU;oBACV,aAAa,WAAA;AAAM,6BAAA;oBAAA;oBACnB,kBAAkB,WAAA;AAAM,6BAAA;oBAAA;oBACxB,kBAAkB;oBAClB,mBAAmB;;AAoVvB,yBAAAA;kBA9VoC,mCAAA,WAAA,CAAe;;AAA9B,kCAAA,SAAA,IAAA;;;;;;;;;;;AC5ErB,cAAAH,QAAA,UAAA;;;;;;;;;", "names": ["module", "exports", "key", "Webcam"]}