{"hash": "5873e72a", "configHash": "d8097e84", "lockfileHash": "90d0d24b", "browserHash": "90eb3a0e", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "14aaf070", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "46bdaaf7", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "8a5cb2fc", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "cd3f485a", "needsInterop": true}, "@heroicons/react/24/outline": {"src": "../../@heroicons/react/24/outline/esm/index.js", "file": "@heroicons_react_24_outline.js", "fileHash": "47e7abe4", "needsInterop": false}, "axios": {"src": "../../axios/index.js", "file": "axios.js", "fileHash": "914f17c7", "needsInterop": false}, "framer-motion": {"src": "../../framer-motion/dist/es/index.mjs", "file": "framer-motion.js", "fileHash": "0d7b4601", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "1876e0d0", "needsInterop": true}, "react-router-dom": {"src": "../../react-router-dom/dist/index.mjs", "file": "react-router-dom.js", "fileHash": "b3430cd8", "needsInterop": false}, "react-webcam": {"src": "../../react-webcam/dist/react-webcam.js", "file": "react-webcam.js", "fileHash": "2dbc862e", "needsInterop": true}, "recharts": {"src": "../../recharts/es6/index.js", "file": "recharts.js", "fileHash": "f8de2836", "needsInterop": false}}, "chunks": {"chunk-MJNCUEZK": {"file": "chunk-MJNCUEZK.js"}, "chunk-HE4GKDYE": {"file": "chunk-HE4GKDYE.js"}, "chunk-UGC3UZ7L": {"file": "chunk-UGC3UZ7L.js"}, "chunk-G3PMV62Z": {"file": "chunk-G3PMV62Z.js"}}}