import React, { useState, useRef, useCallback } from 'react';
import { motion } from 'framer-motion';
import Webcam from 'react-webcam';
import {
  CameraIcon,
  UserPlusIcon,
  PhotoIcon,
  CheckCircleIcon,
  XCircleIcon,
  ArrowPathIcon
} from '@heroicons/react/24/outline';
import { userAPI, base64ToFile } from '../services/api';

const FaceRegistration: React.FC = () => {
  const webcamRef = useRef<Webcam>(null);
  const [capturedImage, setCapturedImage] = useState<string | null>(null);
  const [isCapturing, setIsCapturing] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    department: '',
    position: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');

  const capture = useCallback(() => {
    const imageSrc = webcamRef.current?.getScreenshot();
    if (imageSrc) {
      setCapturedImage(imageSrc);
      setIsCapturing(false);
    }
  }, [webcamRef]);

  const retakePhoto = () => {
    setCapturedImage(null);
    setIsCapturing(true);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!capturedImage || !formData.name) {
      return;
    }

    setIsSubmitting(true);
    setSubmitStatus('idle');

    try {
      // 将base64图像转换为File对象
      const imageFile = base64ToFile(capturedImage, 'face-image.jpg');

      // 调用API注册用户
      await userAPI.register(formData, imageFile);

      setSubmitStatus('success');

      // 重置表单
      setTimeout(() => {
        setFormData({ name: '', email: '', department: '', position: '' });
        setCapturedImage(null);
        setSubmitStatus('idle');
      }, 2000);

    } catch (error) {
      console.error('Registration error:', error);
      setSubmitStatus('error');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center"
      >
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white">人脸注册</h1>
        <p className="text-gray-600 dark:text-gray-400 mt-2">
          请填写用户信息并拍摄清晰的人脸照片
        </p>
      </motion.div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Camera Section */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          className="card p-6"
        >
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
            <CameraIcon className="w-6 h-6 mr-2" />
            人脸拍摄
          </h2>
          
          <div className="space-y-4">
            <div className="relative bg-gray-100 dark:bg-dark-700 rounded-lg overflow-hidden aspect-video">
              {capturedImage ? (
                <img 
                  src={capturedImage} 
                  alt="Captured face" 
                  className="w-full h-full object-cover"
                />
              ) : (
                <Webcam
                  ref={webcamRef}
                  audio={false}
                  screenshotFormat="image/jpeg"
                  className="w-full h-full object-cover"
                  videoConstraints={{
                    width: 640,
                    height: 480,
                    facingMode: "user"
                  }}
                />
              )}
              
              {!capturedImage && (
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="w-48 h-48 border-2 border-dashed border-primary-400 rounded-full flex items-center justify-center">
                    <span className="text-primary-600 dark:text-primary-400 text-sm">
                      请将人脸置于圆圈内
                    </span>
                  </div>
                </div>
              )}
            </div>

            <div className="flex space-x-3">
              {!capturedImage ? (
                <button
                  onClick={capture}
                  className="flex-1 btn-primary flex items-center justify-center space-x-2"
                >
                  <PhotoIcon className="w-5 h-5" />
                  <span>拍摄照片</span>
                </button>
              ) : (
                <button
                  onClick={retakePhoto}
                  className="flex-1 btn-secondary flex items-center justify-center space-x-2"
                >
                  <ArrowPathIcon className="w-5 h-5" />
                  <span>重新拍摄</span>
                </button>
              )}
            </div>

            {capturedImage && (
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                className="p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg"
              >
                <div className="flex items-center space-x-2 text-green-700 dark:text-green-400">
                  <CheckCircleIcon className="w-5 h-5" />
                  <span className="text-sm">照片拍摄成功！</span>
                </div>
              </motion.div>
            )}
          </div>
        </motion.div>

        {/* Form Section */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          className="card p-6"
        >
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
            <UserPlusIcon className="w-6 h-6 mr-2" />
            用户信息
          </h2>

          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                姓名 *
              </label>
              <input
                type="text"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                required
                className="input-field"
                placeholder="请输入姓名"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                邮箱
              </label>
              <input
                type="email"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
                className="input-field"
                placeholder="请输入邮箱地址"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                部门
              </label>
              <select
                name="department"
                value={formData.department}
                onChange={handleInputChange}
                className="input-field"
              >
                <option value="">请选择部门</option>
                <option value="技术部">技术部</option>
                <option value="市场部">市场部</option>
                <option value="人事部">人事部</option>
                <option value="财务部">财务部</option>
                <option value="运营部">运营部</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                职位
              </label>
              <input
                type="text"
                name="position"
                value={formData.position}
                onChange={handleInputChange}
                className="input-field"
                placeholder="请输入职位"
              />
            </div>

            <button
              type="submit"
              disabled={!capturedImage || !formData.name || isSubmitting}
              className={`w-full py-3 px-4 rounded-lg font-medium transition-colors duration-200 flex items-center justify-center space-x-2 ${
                !capturedImage || !formData.name || isSubmitting
                  ? 'bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed'
                  : 'btn-primary'
              }`}
            >
              {isSubmitting ? (
                <>
                  <ArrowPathIcon className="w-5 h-5 animate-spin" />
                  <span>注册中...</span>
                </>
              ) : (
                <>
                  <UserPlusIcon className="w-5 h-5" />
                  <span>注册用户</span>
                </>
              )}
            </button>

            {/* Status Messages */}
            {submitStatus === 'success' && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                className="p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg"
              >
                <div className="flex items-center space-x-2 text-green-700 dark:text-green-400">
                  <CheckCircleIcon className="w-5 h-5" />
                  <span className="text-sm">用户注册成功！</span>
                </div>
              </motion.div>
            )}

            {submitStatus === 'error' && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                className="p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg"
              >
                <div className="flex items-center space-x-2 text-red-700 dark:text-red-400">
                  <XCircleIcon className="w-5 h-5" />
                  <span className="text-sm">注册失败，请重试</span>
                </div>
              </motion.div>
            )}
          </form>
        </motion.div>
      </div>
    </div>
  );
};

export default FaceRegistration;
