# 智能人脸识别系统

一个功能丰富、界面美观的现代化人脸识别系统，基于React + TypeScript前端和Python FastAPI后端构建。

## 🌟 系统特色

### 🎨 美观的用户界面
- 现代化的深色/浅色主题切换
- 响应式设计，支持多设备访问
- 流畅的动画效果和交互体验
- 直观的数据可视化图表

### 🚀 丰富的功能
- **人脸注册**: 支持实时摄像头拍摄和人脸特征提取
- **实时识别**: 连续的人脸识别和结果展示
- **用户管理**: 完整的用户增删改查功能
- **数据分析**: 详细的使用统计和性能分析
- **系统设置**: 灵活的参数配置和系统管理

### 🔧 技术架构
- **前端**: React 18 + TypeScript + Tailwind CSS + Framer Motion
- **后端**: Python FastAPI + OpenCV + face_recognition
- **数据库**: SQLite (可扩展为PostgreSQL)
- **实时通信**: RESTful API

## 📁 项目结构

```
FaceRecognition/
├── face-recognition-frontend/     # React前端应用
│   ├── src/
│   │   ├── components/           # React组件
│   │   │   ├── Dashboard.tsx     # 仪表板
│   │   │   ├── FaceRegistration.tsx  # 人脸注册
│   │   │   ├── FaceRecognition.tsx   # 人脸识别
│   │   │   ├── UserManagement.tsx    # 用户管理
│   │   │   ├── Analytics.tsx     # 数据分析
│   │   │   ├── Settings.tsx      # 系统设置
│   │   │   └── Navbar.tsx        # 导航栏
│   │   ├── services/
│   │   │   └── api.ts            # API服务
│   │   └── ...
│   └── ...
├── face-recognition-backend/      # Python后端API
│   ├── main.py                   # FastAPI主应用
│   ├── requirements.txt          # Python依赖
│   └── start.bat                 # 启动脚本
└── README.md                     # 项目说明
```

## 🚀 快速开始

### 环境要求
- Node.js 16+ 
- Python 3.8+
- 摄像头设备

### 1. 启动前端应用

```bash
cd face-recognition-frontend
npm install
npm run dev
```

前端应用将在 http://localhost:5173 启动

### 2. 启动后端服务

#### Windows用户:
```bash
cd face-recognition-backend
start.bat
```

#### Linux/Mac用户:
```bash
cd face-recognition-backend
pip install -r requirements.txt
python main.py
```

后端API将在 http://localhost:8000 启动

### 3. 访问系统

打开浏览器访问 http://localhost:5173 即可使用系统

## 📖 功能说明

### 🏠 仪表板
- 系统使用统计概览
- 实时数据图表展示
- 最近活动记录
- 系统性能监控

### 👤 人脸注册
- 实时摄像头预览
- 人脸检测和拍摄
- 用户信息录入
- 人脸特征提取和存储

### 🔍 人脸识别
- 实时人脸识别
- 识别结果展示
- 识别历史记录
- 置信度显示

### 👥 用户管理
- 用户列表查看
- 搜索和筛选
- 用户详情查看
- 用户删除功能

### 📊 数据分析
- 识别趋势分析
- 部门使用分布
- 24小时活动分布
- 系统性能指标

### ⚙️ 系统设置
- 识别参数配置
- 摄像头设置
- 安全选项
- 通知设置

## 🔧 API接口

### 用户管理
- `POST /api/users/register` - 注册新用户
- `GET /api/users` - 获取用户列表
- `DELETE /api/users/{user_id}` - 删除用户

### 人脸识别
- `POST /api/recognition/identify` - 识别人脸

### 数据分析
- `GET /api/analytics/stats` - 获取统计数据

## 🛠️ 开发说明

### 前端开发
- 使用 `npm run dev` 启动开发服务器
- 支持热重载，修改代码后自动刷新
- 使用 TypeScript 确保类型安全

### 后端开发
- 使用 `uvicorn main:app --reload` 启动开发服务器
- 支持自动重载，修改代码后自动重启
- 访问 http://localhost:8000/docs 查看API文档

### 数据库
- 系统自动创建SQLite数据库文件
- 包含用户表和识别记录表
- 支持扩展为PostgreSQL等其他数据库

## 🔒 安全特性

- 人脸数据加密存储
- 会话管理和超时控制
- 输入验证和错误处理
- CORS跨域保护

## 🎯 未来规划

- [ ] 支持多人脸同时识别
- [ ] 添加人脸活体检测
- [ ] 集成更多数据库支持
- [ ] 添加用户权限管理
- [ ] 支持人脸识别API调用
- [ ] 移动端适配优化

## 📝 许可证

本项目采用 MIT 许可证

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进项目！

## 📞 联系方式

如有问题或建议，请通过以下方式联系：
- 创建 GitHub Issue
- 发送邮件至项目维护者

---

**享受使用智能人脸识别系统！** 🎉
