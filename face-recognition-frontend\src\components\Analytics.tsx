import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  ChartBarIcon, 
  CalendarIcon,
  TrendingUpIcon,
  TrendingDownIcon,
  UsersIcon,
  EyeIcon,
  ClockIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';
import { 
  <PERSON>Chart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  AreaChart,
  Area
} from 'recharts';

const Analytics: React.FC = () => {
  const [timeRange, setTimeRange] = useState('7d');

  // 模拟数据
  const recognitionTrends = [
    { date: '01-14', recognitions: 45, success: 42, failed: 3 },
    { date: '01-15', recognitions: 52, success: 48, failed: 4 },
    { date: '01-16', recognitions: 38, success: 35, failed: 3 },
    { date: '01-17', recognitions: 61, success: 58, failed: 3 },
    { date: '01-18', recognitions: 49, success: 46, failed: 3 },
    { date: '01-19', recognitions: 67, success: 63, failed: 4 },
    { date: '01-20', recognitions: 73, success: 69, failed: 4 },
  ];

  const hourlyData = [
    { hour: '00', count: 2 }, { hour: '01', count: 1 }, { hour: '02', count: 0 },
    { hour: '03', count: 0 }, { hour: '04', count: 1 }, { hour: '05', count: 3 },
    { hour: '06', count: 8 }, { hour: '07', count: 15 }, { hour: '08', count: 25 },
    { hour: '09', count: 32 }, { hour: '10', count: 28 }, { hour: '11', count: 24 },
    { hour: '12', count: 18 }, { hour: '13', count: 22 }, { hour: '14', count: 26 },
    { hour: '15', count: 24 }, { hour: '16', count: 20 }, { hour: '17', count: 16 },
    { hour: '18', count: 12 }, { hour: '19', count: 8 }, { hour: '20', count: 5 },
    { hour: '21', count: 4 }, { hour: '22', count: 3 }, { hour: '23', count: 2 },
  ];

  const departmentData = [
    { name: '技术部', value: 35, color: '#3b82f6' },
    { name: '市场部', value: 25, color: '#10b981' },
    { name: '人事部', value: 20, color: '#f59e0b' },
    { name: '财务部', value: 15, color: '#ef4444' },
    { name: '运营部', value: 5, color: '#8b5cf6' },
  ];

  const stats = [
    {
      title: '总识别次数',
      value: '2,847',
      change: '+12.5%',
      trend: 'up',
      icon: EyeIcon,
      color: 'bg-blue-500'
    },
    {
      title: '成功率',
      value: '94.2%',
      change: '+2.1%',
      trend: 'up',
      icon: CheckCircleIcon,
      color: 'bg-green-500'
    },
    {
      title: '活跃用户',
      value: '156',
      change: '+8.3%',
      trend: 'up',
      icon: UsersIcon,
      color: 'bg-purple-500'
    },
    {
      title: '平均响应时间',
      value: '0.8s',
      change: '-15.2%',
      trend: 'down',
      icon: ClockIcon,
      color: 'bg-orange-500'
    },
  ];

  const StatCard = ({ stat }: { stat: any }) => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="card p-6"
    >
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
            {stat.title}
          </p>
          <p className="text-2xl font-bold text-gray-900 dark:text-white mt-1">
            {stat.value}
          </p>
          <div className="flex items-center mt-2">
            {stat.trend === 'up' ? (
              <TrendingUpIcon className="w-4 h-4 text-green-500 mr-1" />
            ) : (
              <TrendingDownIcon className="w-4 h-4 text-red-500 mr-1" />
            )}
            <span className={`text-sm ${
              stat.trend === 'up' ? 'text-green-600' : 'text-red-600'
            }`}>
              {stat.change}
            </span>
            <span className="text-sm text-gray-500 dark:text-gray-400 ml-1">
              较上周
            </span>
          </div>
        </div>
        <div className={`p-3 rounded-full ${stat.color}`}>
          <stat.icon className="w-6 h-6 text-white" />
        </div>
      </div>
    </motion.div>
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex justify-between items-center"
      >
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">数据分析</h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            系统使用情况和性能分析
          </p>
        </div>
        
        <div className="flex items-center space-x-2">
          <CalendarIcon className="w-5 h-5 text-gray-400" />
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            className="input-field"
          >
            <option value="7d">最近7天</option>
            <option value="30d">最近30天</option>
            <option value="90d">最近90天</option>
          </select>
        </div>
      </motion.div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <StatCard key={index} stat={stat} />
        ))}
      </div>

      {/* Charts Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recognition Trends */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          className="card p-6"
        >
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            识别趋势分析
          </h3>
          <ResponsiveContainer width="100%" height={300}>
            <AreaChart data={recognitionTrends}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis />
              <Tooltip />
              <Area 
                type="monotone" 
                dataKey="success" 
                stackId="1"
                stroke="#10b981" 
                fill="#10b981"
                fillOpacity={0.6}
              />
              <Area 
                type="monotone" 
                dataKey="failed" 
                stackId="1"
                stroke="#ef4444" 
                fill="#ef4444"
                fillOpacity={0.6}
              />
            </AreaChart>
          </ResponsiveContainer>
        </motion.div>

        {/* Department Distribution */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          className="card p-6"
        >
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            部门使用分布
          </h3>
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={departmentData}
                cx="50%"
                cy="50%"
                outerRadius={80}
                dataKey="value"
                label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
              >
                {departmentData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip />
            </PieChart>
          </ResponsiveContainer>
        </motion.div>
      </div>

      {/* Hourly Activity */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="card p-6"
      >
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          24小时活动分布
        </h3>
        <ResponsiveContainer width="100%" height={300}>
          <BarChart data={hourlyData}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="hour" />
            <YAxis />
            <Tooltip />
            <Bar dataKey="count" fill="#3b82f6" />
          </BarChart>
        </ResponsiveContainer>
      </motion.div>

      {/* Performance Metrics */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="card p-6"
        >
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            识别准确率
          </h3>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600 dark:text-gray-400">优秀 (95%+)</span>
              <span className="text-sm font-medium text-gray-900 dark:text-white">78%</span>
            </div>
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div className="bg-green-500 h-2 rounded-full" style={{ width: '78%' }}></div>
            </div>
            
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600 dark:text-gray-400">良好 (90-95%)</span>
              <span className="text-sm font-medium text-gray-900 dark:text-white">18%</span>
            </div>
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div className="bg-yellow-500 h-2 rounded-full" style={{ width: '18%' }}></div>
            </div>
            
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600 dark:text-gray-400">需改进 (&lt;90%)</span>
              <span className="text-sm font-medium text-gray-900 dark:text-white">4%</span>
            </div>
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div className="bg-red-500 h-2 rounded-full" style={{ width: '4%' }}></div>
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="card p-6"
        >
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            系统性能
          </h3>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600 dark:text-gray-400">CPU使用率</span>
              <span className="text-sm font-medium text-gray-900 dark:text-white">45%</span>
            </div>
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div className="bg-blue-500 h-2 rounded-full" style={{ width: '45%' }}></div>
            </div>
            
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600 dark:text-gray-400">内存使用率</span>
              <span className="text-sm font-medium text-gray-900 dark:text-white">62%</span>
            </div>
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div className="bg-green-500 h-2 rounded-full" style={{ width: '62%' }}></div>
            </div>
            
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600 dark:text-gray-400">存储使用率</span>
              <span className="text-sm font-medium text-gray-900 dark:text-white">38%</span>
            </div>
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div className="bg-purple-500 h-2 rounded-full" style={{ width: '38%' }}></div>
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="card p-6"
        >
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            最近警告
          </h3>
          <div className="space-y-3">
            <div className="flex items-start space-x-3">
              <div className="w-2 h-2 bg-yellow-500 rounded-full mt-2"></div>
              <div>
                <p className="text-sm text-gray-900 dark:text-white">识别延迟增加</p>
                <p className="text-xs text-gray-500 dark:text-gray-400">2小时前</p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <div className="w-2 h-2 bg-red-500 rounded-full mt-2"></div>
              <div>
                <p className="text-sm text-gray-900 dark:text-white">摄像头连接异常</p>
                <p className="text-xs text-gray-500 dark:text-gray-400">5小时前</p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
              <div>
                <p className="text-sm text-gray-900 dark:text-white">系统更新完成</p>
                <p className="text-xs text-gray-500 dark:text-gray-400">1天前</p>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default Analytics;
