{"version": 3, "file": "symlink-error.js", "sourceRoot": "", "sources": ["../../src/symlink-error.ts"], "names": [], "mappings": "AAAA,MAAM,OAAO,YAAa,SAAQ,KAAK;IACrC,IAAI,CAAQ;IACZ,OAAO,CAAQ;IACf,OAAO,GAAc,SAAS,CAAA;IAC9B,IAAI,GAAwB,mBAAmB,CAAA;IAC/C,YAAY,OAAe,EAAE,IAAY;QACvC,KAAK,CAAC,yDAAyD,CAAC,CAAA;QAChE,IAAI,CAAC,OAAO,GAAG,OAAO,CAAA;QACtB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;IAClB,CAAC;IACD,IAAI,IAAI;QACN,OAAO,cAAc,CAAA;IACvB,CAAC;CACF", "sourcesContent": ["export class SymlinkError extends Error {\n  path: string\n  symlink: string\n  syscall: 'symlink' = 'symlink'\n  code: 'TAR_SYMLINK_ERROR' = 'TAR_SYMLINK_ERROR'\n  constructor(symlink: string, path: string) {\n    super('TAR_SYMLINK_ERROR: Cannot extract through symbolic link')\n    this.symlink = symlink\n    this.path = path\n  }\n  get name() {\n    return 'SymlinkError'\n  }\n}\n"]}