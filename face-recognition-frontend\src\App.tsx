import React, { useState, useEffect, Suspense } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { motion } from 'framer-motion';
import ErrorBoundary from './components/ErrorBoundary';
import LoadingScreen from './components/LoadingScreen';
import Navbar from './components/Navbar';
import Dashboard from './components/Dashboard';
import FaceRegistration from './components/FaceRegistration';
import FaceRecognition from './components/FaceRecognition';
import UserManagement from './components/UserManagement';
import Analytics from './components/Analytics';
import Settings from './components/Settings';

function App() {
  const [darkMode, setDarkMode] = useState(false);

  useEffect(() => {
    const savedTheme = localStorage.getItem('theme');
    if (savedTheme === 'dark') {
      setDarkMode(true);
      document.documentElement.classList.add('dark');
    }
  }, []);

  const toggleDarkMode = () => {
    setDarkMode(!darkMode);
    if (!darkMode) {
      document.documentElement.classList.add('dark');
      localStorage.setItem('theme', 'dark');
    } else {
      document.documentElement.classList.remove('dark');
      localStorage.setItem('theme', 'light');
    }
  };

  return (
    <ErrorBoundary>
      <Router>
        <div className={`min-h-screen bg-gray-50 transition-colors duration-300`}>
          <Navbar darkMode={darkMode} toggleDarkMode={toggleDarkMode} />

          <Suspense fallback={<LoadingScreen />}>
            <motion.main
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="container mx-auto px-4 py-8"
            >
              <Routes>
                <Route path="/" element={<Dashboard />} />
                <Route path="/register" element={<FaceRegistration />} />
                <Route path="/recognize" element={<FaceRecognition />} />
                <Route path="/users" element={<UserManagement />} />
                <Route path="/analytics" element={<Analytics />} />
                <Route path="/settings" element={<Settings />} />
              </Routes>
            </motion.main>
          </Suspense>
        </div>
      </Router>
    </ErrorBoundary>
  );
}

export default App;
