from fastapi import <PERSON><PERSON><PERSON>, File, UploadFile, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import cv2
import face_recognition
import numpy as np
import sqlite3
import json
import base64
from datetime import datetime
from typing import List, Optional
from pydantic import BaseModel
import os
import uuid

app = FastAPI(title="智能人脸识别系统 API", version="1.0.0")

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173"],  # React开发服务器
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 数据模型
class UserCreate(BaseModel):
    name: str
    email: Optional[str] = None
    department: Optional[str] = None
    position: Optional[str] = None

class UserResponse(BaseModel):
    id: str
    name: str
    email: Optional[str]
    department: Optional[str]
    position: Optional[str]
    registered_at: datetime
    status: str

class RecognitionResult(BaseModel):
    user_id: Optional[str]
    name: Optional[str]
    confidence: float
    timestamp: datetime
    status: str

# 数据库初始化
def init_database():
    conn = sqlite3.connect('face_recognition.db')
    cursor = conn.cursor()
    
    # 创建用户表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS users (
            id TEXT PRIMARY KEY,
            name TEXT NOT NULL,
            email TEXT,
            department TEXT,
            position TEXT,
            face_encoding TEXT NOT NULL,
            registered_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            status TEXT DEFAULT 'active'
        )
    ''')
    
    # 创建识别记录表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS recognition_logs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id TEXT,
            confidence REAL,
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            status TEXT,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
    ''')
    
    conn.commit()
    conn.close()

# 初始化数据库
init_database()

# 工具函数
def encode_face_from_image(image_data: bytes):
    """从图像数据中提取人脸编码"""
    # 将字节数据转换为numpy数组
    nparr = np.frombuffer(image_data, np.uint8)
    image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
    
    # 转换为RGB格式
    rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    
    # 检测人脸位置
    face_locations = face_recognition.face_locations(rgb_image)
    
    if not face_locations:
        raise HTTPException(status_code=400, detail="未检测到人脸")
    
    if len(face_locations) > 1:
        raise HTTPException(status_code=400, detail="检测到多张人脸，请确保图像中只有一张人脸")
    
    # 提取人脸编码
    face_encodings = face_recognition.face_encodings(rgb_image, face_locations)
    
    if not face_encodings:
        raise HTTPException(status_code=400, detail="无法提取人脸特征")
    
    return face_encodings[0]

def get_all_users_encodings():
    """获取所有用户的人脸编码"""
    conn = sqlite3.connect('face_recognition.db')
    cursor = conn.cursor()
    
    cursor.execute("SELECT id, name, face_encoding FROM users WHERE status = 'active'")
    users = cursor.fetchall()
    conn.close()
    
    known_encodings = []
    known_names = []
    known_ids = []
    
    for user_id, name, encoding_str in users:
        encoding = np.array(json.loads(encoding_str))
        known_encodings.append(encoding)
        known_names.append(name)
        known_ids.append(user_id)
    
    return known_encodings, known_names, known_ids

# API路由
@app.get("/")
async def root():
    return {"message": "智能人脸识别系统 API"}

@app.post("/api/users/register", response_model=UserResponse)
async def register_user(
    user_data: UserCreate,
    face_image: UploadFile = File(...)
):
    """注册新用户"""
    try:
        # 读取上传的图像
        image_data = await face_image.read()
        
        # 提取人脸编码
        face_encoding = encode_face_from_image(image_data)
        
        # 生成用户ID
        user_id = str(uuid.uuid4())
        
        # 保存到数据库
        conn = sqlite3.connect('face_recognition.db')
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO users (id, name, email, department, position, face_encoding)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (
            user_id,
            user_data.name,
            user_data.email,
            user_data.department,
            user_data.position,
            json.dumps(face_encoding.tolist())
        ))
        
        conn.commit()
        conn.close()
        
        return UserResponse(
            id=user_id,
            name=user_data.name,
            email=user_data.email,
            department=user_data.department,
            position=user_data.position,
            registered_at=datetime.now(),
            status="active"
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"注册失败: {str(e)}")

@app.post("/api/recognition/identify", response_model=RecognitionResult)
async def identify_face(face_image: UploadFile = File(...)):
    """识别人脸"""
    try:
        # 读取上传的图像
        image_data = await face_image.read()
        
        # 提取人脸编码
        unknown_encoding = encode_face_from_image(image_data)
        
        # 获取所有已知用户的编码
        known_encodings, known_names, known_ids = get_all_users_encodings()
        
        if not known_encodings:
            # 记录未知用户识别
            conn = sqlite3.connect('face_recognition.db')
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO recognition_logs (user_id, confidence, status)
                VALUES (?, ?, ?)
            ''', (None, 0.0, 'unknown'))
            conn.commit()
            conn.close()
            
            return RecognitionResult(
                user_id=None,
                name="未知用户",
                confidence=0.0,
                timestamp=datetime.now(),
                status="unknown"
            )
        
        # 比较人脸
        matches = face_recognition.compare_faces(known_encodings, unknown_encoding, tolerance=0.6)
        face_distances = face_recognition.face_distance(known_encodings, unknown_encoding)
        
        if True in matches:
            best_match_index = np.argmin(face_distances)
            confidence = (1 - face_distances[best_match_index]) * 100
            
            user_id = known_ids[best_match_index]
            name = known_names[best_match_index]
            
            # 记录识别结果
            conn = sqlite3.connect('face_recognition.db')
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO recognition_logs (user_id, confidence, status)
                VALUES (?, ?, ?)
            ''', (user_id, confidence, 'success'))
            conn.commit()
            conn.close()
            
            return RecognitionResult(
                user_id=user_id,
                name=name,
                confidence=confidence,
                timestamp=datetime.now(),
                status="success"
            )
        else:
            # 记录识别失败
            conn = sqlite3.connect('face_recognition.db')
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO recognition_logs (user_id, confidence, status)
                VALUES (?, ?, ?)
            ''', (None, 0.0, 'failed'))
            conn.commit()
            conn.close()
            
            return RecognitionResult(
                user_id=None,
                name="未知用户",
                confidence=0.0,
                timestamp=datetime.now(),
                status="failed"
            )
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"识别失败: {str(e)}")

@app.get("/api/users", response_model=List[UserResponse])
async def get_users():
    """获取所有用户"""
    conn = sqlite3.connect('face_recognition.db')
    cursor = conn.cursor()
    
    cursor.execute('''
        SELECT id, name, email, department, position, registered_at, status
        FROM users
        ORDER BY registered_at DESC
    ''')
    
    users = cursor.fetchall()
    conn.close()
    
    return [
        UserResponse(
            id=user[0],
            name=user[1],
            email=user[2],
            department=user[3],
            position=user[4],
            registered_at=datetime.fromisoformat(user[5]),
            status=user[6]
        )
        for user in users
    ]

@app.get("/api/analytics/stats")
async def get_analytics_stats():
    """获取分析统计数据"""
    conn = sqlite3.connect('face_recognition.db')
    cursor = conn.cursor()
    
    # 总用户数
    cursor.execute("SELECT COUNT(*) FROM users WHERE status = 'active'")
    total_users = cursor.fetchone()[0]
    
    # 今日识别次数
    cursor.execute('''
        SELECT COUNT(*) FROM recognition_logs 
        WHERE DATE(timestamp) = DATE('now')
    ''')
    today_recognitions = cursor.fetchone()[0]
    
    # 成功率
    cursor.execute('''
        SELECT 
            COUNT(CASE WHEN status = 'success' THEN 1 END) * 100.0 / COUNT(*) as success_rate
        FROM recognition_logs 
        WHERE DATE(timestamp) >= DATE('now', '-7 days')
    ''')
    success_rate = cursor.fetchone()[0] or 0
    
    conn.close()
    
    return {
        "total_users": total_users,
        "today_recognitions": today_recognitions,
        "success_rate": round(success_rate, 1),
        "active_users": total_users  # 简化处理
    }

@app.delete("/api/users/{user_id}")
async def delete_user(user_id: str):
    """删除用户"""
    conn = sqlite3.connect('face_recognition.db')
    cursor = conn.cursor()
    
    cursor.execute("UPDATE users SET status = 'deleted' WHERE id = ?", (user_id,))
    
    if cursor.rowcount == 0:
        conn.close()
        raise HTTPException(status_code=404, detail="用户不存在")
    
    conn.commit()
    conn.close()
    
    return {"message": "用户已删除"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
