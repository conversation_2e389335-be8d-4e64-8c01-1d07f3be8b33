@echo off
echo ========================================
echo    智能人脸识别系统启动脚本
echo ========================================
echo.

echo 正在启动后端服务...
start "Face Recognition Backend" cmd /k "cd face-recognition-backend && python main.py"

echo 等待后端服务启动...
timeout /t 5 /nobreak > nul

echo 正在启动前端应用...
start "Face Recognition Frontend" cmd /k "cd face-recognition-frontend && npm run dev"

echo.
echo ========================================
echo 系统启动完成！
echo.
echo 前端地址: http://localhost:5173
echo 后端API: http://localhost:8000
echo API文档: http://localhost:8000/docs
echo ========================================
echo.
echo 按任意键退出...
pause > nul
