import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  UsersIcon,
  CameraIcon,
  ChartBarIcon,
  ClockIcon,
  EyeIcon,
  UserGroupIcon,
  ArrowTrendingUpIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar } from 'recharts';
import { analyticsAPI } from '../services/api';
import type { AnalyticsStats } from '../services/api';

const Dashboard: React.FC = () => {
  const [stats, setStats] = useState<AnalyticsStats>({
    total_users: 0,
    today_recognitions: 0,
    success_rate: 0,
    active_users: 0
  });

  // 获取统计数据
  useEffect(() => {
    fetchStats();
  }, []);

  const fetchStats = async () => {
    try {
      const data = await analyticsAPI.getStats();
      setStats(data);
    } catch (error) {
      console.error('Failed to fetch stats:', error);
    }
  };

  const [recentActivity] = useState([
    { id: 1, user: '张三', action: '人脸识别成功', time: '2分钟前', status: 'success' },
    { id: 2, user: '李四', action: '新用户注册', time: '5分钟前', status: 'info' },
    { id: 3, user: '王五', action: '人脸识别失败', time: '8分钟前', status: 'error' },
    { id: 4, user: '赵六', action: '人脸识别成功', time: '12分钟前', status: 'success' },
    { id: 5, user: '钱七', action: '用户信息更新', time: '15分钟前', status: 'info' },
  ]);

  const [chartData] = useState([
    { name: '周一', recognitions: 65, registrations: 8 },
    { name: '周二', recognitions: 78, registrations: 12 },
    { name: '周三', recognitions: 90, registrations: 15 },
    { name: '周四', recognitions: 81, registrations: 9 },
    { name: '周五', recognitions: 95, registrations: 18 },
    { name: '周六', recognitions: 72, registrations: 6 },
    { name: '周日', recognitions: 89, registrations: 11 },
  ]);

  interface StatCardProps {
    icon: React.ComponentType<{ className?: string }>;
    title: string;
    value: string | number;
    change?: number;
    color: string;
  }

  const StatCard = ({ icon: Icon, title, value, change, color }: StatCardProps) => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="card p-6"
    >
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600 dark:text-gray-400">{title}</p>
          <p className="text-2xl font-bold text-gray-900 dark:text-white mt-1">{value}</p>
          {change && (
            <p className={`text-sm mt-1 ${change > 0 ? 'text-green-600' : 'text-red-600'}`}>
              {change > 0 ? '+' : ''}{change}% 较昨日
            </p>
          )}
        </div>
        <div className={`p-3 rounded-full ${color}`}>
          <Icon className="w-6 h-6 text-white" />
        </div>
      </div>
    </motion.div>
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex justify-between items-center"
      >
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">仪表板</h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">欢迎使用智能人脸识别系统</p>
        </div>
        <div className="text-right">
          <p className="text-sm text-gray-600 dark:text-gray-400">今日日期</p>
          <p className="text-lg font-semibold text-gray-900 dark:text-white">
            {new Date().toLocaleDateString('zh-CN')}
          </p>
        </div>
      </motion.div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          icon={UsersIcon}
          title="总用户数"
          value={stats.total_users}
          change={12}
          color="bg-blue-500"
        />
        <StatCard
          icon={EyeIcon}
          title="今日识别次数"
          value={stats.today_recognitions}
          change={8}
          color="bg-green-500"
        />
        <StatCard
          icon={ArrowTrendingUpIcon}
          title="识别成功率"
          value={`${stats.success_rate}%`}
          change={2.1}
          color="bg-purple-500"
        />
        <StatCard
          icon={UserGroupIcon}
          title="活跃用户"
          value={stats.active_users}
          change={-3}
          color="bg-orange-500"
        />
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recognition Trends */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          className="card p-6"
        >
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            本周识别趋势
          </h3>
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={chartData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              <Line 
                type="monotone" 
                dataKey="recognitions" 
                stroke="#3b82f6" 
                strokeWidth={2}
                dot={{ fill: '#3b82f6' }}
              />
            </LineChart>
          </ResponsiveContainer>
        </motion.div>

        {/* Registration Stats */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          className="card p-6"
        >
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            本周注册统计
          </h3>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={chartData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              <Bar dataKey="registrations" fill="#10b981" />
            </BarChart>
          </ResponsiveContainer>
        </motion.div>
      </div>

      {/* Recent Activity */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="card p-6"
      >
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          最近活动
        </h3>
        <div className="space-y-4">
          {recentActivity.map((activity, index) => (
            <motion.div
              key={activity.id}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 }}
              className="flex items-center justify-between p-3 bg-gray-50 dark:bg-dark-700 rounded-lg"
            >
              <div className="flex items-center space-x-3">
                <div className={`w-2 h-2 rounded-full ${
                  activity.status === 'success' ? 'bg-green-500' :
                  activity.status === 'error' ? 'bg-red-500' : 'bg-blue-500'
                }`} />
                <div>
                  <p className="text-sm font-medium text-gray-900 dark:text-white">
                    {activity.user} - {activity.action}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    {activity.time}
                  </p>
                </div>
              </div>
              <CheckCircleIcon className={`w-5 h-5 ${
                activity.status === 'success' ? 'text-green-500' :
                activity.status === 'error' ? 'text-red-500' : 'text-blue-500'
              }`} />
            </motion.div>
          ))}
        </div>
      </motion.div>
    </div>
  );
};

export default Dashboard;
