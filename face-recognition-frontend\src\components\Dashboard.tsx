import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  UsersIcon,
  CameraIcon,
  ChartBarIcon,
  ClockIcon,
  EyeIcon,
  UserGroupIcon,
  ArrowTrendingUpIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar } from 'recharts';
import { analyticsAPI, type AnalyticsStats } from '../services/api';

const Dashboard: React.FC = () => {
  const [stats, setStats] = useState<AnalyticsStats>({
    total_users: 0,
    today_recognitions: 0,
    success_rate: 0,
    active_users: 0
  });

  // 获取统计数据
  useEffect(() => {
    fetchStats();
  }, []);

  const fetchStats = async () => {
    try {
      const data = await analyticsAPI.getStats();
      setStats(data);
    } catch (error) {
      console.error('Failed to fetch stats:', error);
    }
  };

  const [recentActivity] = useState([
    { id: 1, user: '张三', action: '人脸识别成功', time: '2分钟前', status: 'success' },
    { id: 2, user: '李四', action: '新用户注册', time: '5分钟前', status: 'info' },
    { id: 3, user: '王五', action: '人脸识别失败', time: '8分钟前', status: 'error' },
    { id: 4, user: '赵六', action: '人脸识别成功', time: '12分钟前', status: 'success' },
    { id: 5, user: '钱七', action: '用户信息更新', time: '15分钟前', status: 'info' },
  ]);

  const [chartData] = useState([
    { name: '周一', recognitions: 65, registrations: 8 },
    { name: '周二', recognitions: 78, registrations: 12 },
    { name: '周三', recognitions: 90, registrations: 15 },
    { name: '周四', recognitions: 81, registrations: 9 },
    { name: '周五', recognitions: 95, registrations: 18 },
    { name: '周六', recognitions: 72, registrations: 6 },
    { name: '周日', recognitions: 89, registrations: 11 },
  ]);

  interface StatCardProps {
    icon: React.ComponentType<{ className?: string }>;
    title: string;
    value: string | number;
    change?: number;
    color: string;
  }

  const StatCard = ({ icon: Icon, title, value, change, color }: StatCardProps) => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      whileHover={{ y: -5, transition: { duration: 0.2 } }}
      className="relative overflow-hidden bg-white rounded-2xl shadow-lg border border-gray-100 p-6 hover:shadow-xl transition-all duration-300"
    >
      {/* 背景装饰 */}
      <div className="absolute top-0 right-0 w-32 h-32 transform translate-x-8 -translate-y-8">
        <div className={`w-full h-full rounded-full opacity-10 ${color}`}></div>
      </div>

      <div className="relative z-10">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <p className="text-sm font-medium text-gray-500 mb-2">{title}</p>
            <p className="text-3xl font-bold text-gray-900 mb-3">{value}</p>
            {change && (
              <div className="flex items-center">
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                  change > 0
                    ? 'bg-green-100 text-green-800'
                    : 'bg-red-100 text-red-800'
                }`}>
                  {change > 0 ? '↗' : '↘'} {Math.abs(change)}%
                </span>
                <span className="text-xs text-gray-500 ml-2">较昨日</span>
              </div>
            )}
          </div>
          <div className={`p-4 rounded-xl ${color} shadow-lg`}>
            <Icon className="w-7 h-7 text-white" />
          </div>
        </div>
      </div>
    </motion.div>
  );

  return (
    <div className="space-y-8 animate-fade-in">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="relative overflow-hidden bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 rounded-3xl p-8 mb-8"
      >
        {/* 背景装饰 */}
        <div className="absolute inset-0 bg-black opacity-10"></div>
        <div className="absolute top-0 right-0 w-64 h-64 transform translate-x-32 -translate-y-32">
          <div className="w-full h-full rounded-full bg-white opacity-10"></div>
        </div>
        <div className="absolute bottom-0 left-0 w-48 h-48 transform -translate-x-24 translate-y-24">
          <div className="w-full h-full rounded-full bg-white opacity-5"></div>
        </div>

        <div className="relative z-10 flex justify-between items-center text-white">
          <div>
            <motion.h1
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.2 }}
              className="text-4xl font-bold mb-2"
            >
              智能人脸识别系统
            </motion.h1>
            <motion.p
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.3 }}
              className="text-blue-100 text-lg"
            >
              欢迎回来！系统运行正常，数据实时更新
            </motion.p>
          </div>
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.4 }}
            className="text-right bg-white bg-opacity-20 rounded-2xl p-4 backdrop-blur-sm"
          >
            <p className="text-blue-100 text-sm mb-1">今日日期</p>
            <p className="text-2xl font-bold">
              {new Date().toLocaleDateString('zh-CN')}
            </p>
            <p className="text-blue-100 text-sm mt-1">
              {new Date().toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })}
            </p>
          </motion.div>
        </div>
      </motion.div>

      {/* Stats Cards */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
        className="mb-6"
      >
        <h2 className="text-2xl font-bold text-gray-900 mb-2">系统概览</h2>
        <p className="text-gray-600">实时监控系统关键指标</p>
      </motion.div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          icon={UsersIcon}
          title="总用户数"
          value={stats.total_users}
          change={12}
          color="bg-gradient-to-r from-blue-500 to-blue-600"
        />
        <StatCard
          icon={EyeIcon}
          title="今日识别次数"
          value={stats.today_recognitions}
          change={8}
          color="bg-gradient-to-r from-green-500 to-emerald-600"
        />
        <StatCard
          icon={ArrowTrendingUpIcon}
          title="识别成功率"
          value={`${stats.success_rate}%`}
          change={2.1}
          color="bg-gradient-to-r from-purple-500 to-indigo-600"
        />
        <StatCard
          icon={UserGroupIcon}
          title="活跃用户"
          value={stats.active_users}
          change={-3}
          color="bg-gradient-to-r from-orange-500 to-red-500"
        />
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Recognition Trends */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6 hover:shadow-xl transition-all duration-300"
        >
          <div className="flex items-center justify-between mb-6">
            <div>
              <h3 className="text-xl font-bold text-gray-900 mb-1">
                本周识别趋势
              </h3>
              <p className="text-sm text-gray-500">每日识别次数统计</p>
            </div>
            <div className="p-3 bg-blue-100 rounded-xl">
              <ChartBarIcon className="w-6 h-6 text-blue-600" />
            </div>
          </div>
          <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-4">
            <ResponsiveContainer width="100%" height={280}>
              <LineChart data={chartData}>
                <CartesianGrid strokeDasharray="3 3" stroke="#e0e7ff" />
                <XAxis
                  dataKey="name"
                  axisLine={false}
                  tickLine={false}
                  tick={{ fill: '#6b7280', fontSize: 12 }}
                />
                <YAxis
                  axisLine={false}
                  tickLine={false}
                  tick={{ fill: '#6b7280', fontSize: 12 }}
                />
                <Tooltip
                  contentStyle={{
                    backgroundColor: 'white',
                    border: 'none',
                    borderRadius: '12px',
                    boxShadow: '0 10px 25px rgba(0,0,0,0.1)'
                  }}
                />
                <Line
                  type="monotone"
                  dataKey="recognitions"
                  stroke="url(#blueGradient)"
                  strokeWidth={3}
                  dot={{ fill: '#3b82f6', strokeWidth: 2, r: 5 }}
                  activeDot={{ r: 7, fill: '#1d4ed8' }}
                />
                <defs>
                  <linearGradient id="blueGradient" x1="0" y1="0" x2="1" y2="0">
                    <stop offset="0%" stopColor="#3b82f6" />
                    <stop offset="100%" stopColor="#1d4ed8" />
                  </linearGradient>
                </defs>
              </LineChart>
            </ResponsiveContainer>
          </div>
        </motion.div>

        {/* Registration Stats */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6 hover:shadow-xl transition-all duration-300"
        >
          <div className="flex items-center justify-between mb-6">
            <div>
              <h3 className="text-xl font-bold text-gray-900 mb-1">
                本周注册统计
              </h3>
              <p className="text-sm text-gray-500">新用户注册趋势</p>
            </div>
            <div className="p-3 bg-green-100 rounded-xl">
              <UsersIcon className="w-6 h-6 text-green-600" />
            </div>
          </div>
          <div className="bg-gradient-to-br from-green-50 to-emerald-50 rounded-xl p-4">
            <ResponsiveContainer width="100%" height={280}>
              <BarChart data={chartData}>
                <CartesianGrid strokeDasharray="3 3" stroke="#d1fae5" />
                <XAxis
                  dataKey="name"
                  axisLine={false}
                  tickLine={false}
                  tick={{ fill: '#6b7280', fontSize: 12 }}
                />
                <YAxis
                  axisLine={false}
                  tickLine={false}
                  tick={{ fill: '#6b7280', fontSize: 12 }}
                />
                <Tooltip
                  contentStyle={{
                    backgroundColor: 'white',
                    border: 'none',
                    borderRadius: '12px',
                    boxShadow: '0 10px 25px rgba(0,0,0,0.1)'
                  }}
                />
                <Bar
                  dataKey="registrations"
                  fill="url(#greenGradient)"
                  radius={[6, 6, 0, 0]}
                />
                <defs>
                  <linearGradient id="greenGradient" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="0%" stopColor="#10b981" />
                    <stop offset="100%" stopColor="#059669" />
                  </linearGradient>
                </defs>
              </BarChart>
            </ResponsiveContainer>
          </div>
        </motion.div>
      </div>

      {/* Recent Activity */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6 hover:shadow-xl transition-all duration-300"
      >
        <div className="flex items-center justify-between mb-6">
          <div>
            <h3 className="text-xl font-bold text-gray-900 mb-1">
              最近活动
            </h3>
            <p className="text-sm text-gray-500">系统实时活动记录</p>
          </div>
          <div className="p-3 bg-purple-100 rounded-xl">
            <ClockIcon className="w-6 h-6 text-purple-600" />
          </div>
        </div>

        <div className="space-y-3">
          {recentActivity.map((activity, index) => (
            <motion.div
              key={activity.id}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 }}
              whileHover={{ x: 5, transition: { duration: 0.2 } }}
              className="group relative overflow-hidden bg-gradient-to-r from-gray-50 to-gray-100 hover:from-blue-50 hover:to-indigo-50 rounded-xl p-4 transition-all duration-300"
            >
              {/* 状态指示条 */}
              <div className={`absolute left-0 top-0 bottom-0 w-1 ${
                activity.status === 'success' ? 'bg-green-500' :
                activity.status === 'error' ? 'bg-red-500' : 'bg-blue-500'
              }`} />

              <div className="flex items-center justify-between ml-4">
                <div className="flex items-center space-x-4">
                  {/* 用户头像 */}
                  <div className={`w-10 h-10 rounded-full flex items-center justify-center text-white font-semibold ${
                    activity.status === 'success' ? 'bg-green-500' :
                    activity.status === 'error' ? 'bg-red-500' : 'bg-blue-500'
                  }`}>
                    {activity.user.charAt(0)}
                  </div>

                  <div>
                    <p className="font-medium text-gray-900 group-hover:text-blue-700 transition-colors">
                      {activity.user}
                    </p>
                    <p className="text-sm text-gray-600 group-hover:text-blue-600 transition-colors">
                      {activity.action}
                    </p>
                    <p className="text-xs text-gray-400 mt-1">
                      {activity.time}
                    </p>
                  </div>
                </div>

                {/* 状态图标 */}
                <div className={`p-2 rounded-lg ${
                  activity.status === 'success' ? 'bg-green-100' :
                  activity.status === 'error' ? 'bg-red-100' : 'bg-blue-100'
                }`}>
                  <CheckCircleIcon className={`w-5 h-5 ${
                    activity.status === 'success' ? 'text-green-600' :
                    activity.status === 'error' ? 'text-red-600' : 'text-blue-600'
                  }`} />
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* 查看更多按钮 */}
        <motion.button
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          className="w-full mt-4 py-3 text-sm font-medium text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-xl transition-all duration-200"
        >
          查看全部活动 →
        </motion.button>
      </motion.div>
    </div>
  );
};

export default Dashboard;
