import React, { useState, useEffect } from 'react';
import { motion, useAnimation, useInView } from 'framer-motion';
import {
  UsersIcon,
  CameraIcon,
  ChartBarIcon,
  ClockIcon,
  EyeIcon,
  UserGroupIcon,
  ArrowTrendingUpIcon,
  CheckCircleIcon,
  SparklesIcon,
  BoltIcon,
  ShieldCheckIcon
} from '@heroicons/react/24/outline';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar, Area, AreaChart } from 'recharts';
import { analyticsAPI, type AnalyticsStats } from '../services/api';

// Apple风格的动画变体
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.2
    }
  }
};

const itemVariants = {
  hidden: { y: 30, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      type: "spring",
      stiffness: 100,
      damping: 12
    }
  }
};

const floatingVariants = {
  animate: {
    y: [0, -10, 0],
    transition: {
      duration: 3,
      repeat: Infinity,
      ease: "easeInOut"
    }
  }
};

const Dashboard: React.FC = () => {
  const [stats, setStats] = useState<AnalyticsStats>({
    total_users: 0,
    today_recognitions: 0,
    success_rate: 0,
    active_users: 0
  });

  const [currentTime, setCurrentTime] = useState(new Date());

  // 获取统计数据
  useEffect(() => {
    fetchStats();
    // 更新时间
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);
    return () => clearInterval(timer);
  }, []);

  const fetchStats = async () => {
    try {
      const data = await analyticsAPI.getStats();
      setStats(data);
    } catch (error) {
      console.error('Failed to fetch stats:', error);
    }
  };

  const [recentActivity] = useState([
    { id: 1, user: '张三', action: '人脸识别成功', time: '2分钟前', status: 'success' },
    { id: 2, user: '李四', action: '新用户注册', time: '5分钟前', status: 'info' },
    { id: 3, user: '王五', action: '人脸识别失败', time: '8分钟前', status: 'error' },
    { id: 4, user: '赵六', action: '人脸识别成功', time: '12分钟前', status: 'success' },
    { id: 5, user: '钱七', action: '用户信息更新', time: '15分钟前', status: 'info' },
  ]);

  const [chartData] = useState([
    { name: '周一', recognitions: 65, registrations: 8 },
    { name: '周二', recognitions: 78, registrations: 12 },
    { name: '周三', recognitions: 90, registrations: 15 },
    { name: '周四', recognitions: 81, registrations: 9 },
    { name: '周五', recognitions: 95, registrations: 18 },
    { name: '周六', recognitions: 72, registrations: 6 },
    { name: '周日', recognitions: 89, registrations: 11 },
  ]);

  interface StatCardProps {
    icon: React.ComponentType<{ className?: string }>;
    title: string;
    value: string | number;
    change?: number;
    color: string;
    gradient: string;
    index: number;
  }

  const StatCard = ({ icon: Icon, title, value, change, color, gradient, index }: StatCardProps) => (
    <motion.div
      variants={itemVariants}
      whileHover={{
        scale: 1.02,
        y: -8,
        transition: {
          type: "spring",
          stiffness: 400,
          damping: 25
        }
      }}
      whileTap={{ scale: 0.98 }}
      className="relative overflow-hidden bg-white/80 backdrop-blur-xl rounded-3xl border border-white/20 shadow-2xl"
      style={{
        background: `linear-gradient(135deg, ${gradient})`,
        boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.2)'
      }}
    >
      {/* 玻璃效果背景 */}
      <div className="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent" />

      {/* 浮动装饰元素 */}
      <motion.div
        variants={floatingVariants}
        animate="animate"
        className="absolute -top-4 -right-4 w-24 h-24 rounded-full bg-white/10 blur-xl"
        style={{ animationDelay: `${index * 0.5}s` }}
      />

      <div className="relative z-10 p-8">
        {/* 图标区域 */}
        <motion.div
          initial={{ scale: 0, rotate: -180 }}
          animate={{ scale: 1, rotate: 0 }}
          transition={{
            delay: index * 0.1 + 0.3,
            type: "spring",
            stiffness: 200,
            damping: 15
          }}
          className={`inline-flex p-4 rounded-2xl ${color} mb-6 shadow-lg`}
        >
          <Icon className="w-8 h-8 text-white" />
        </motion.div>

        {/* 数值显示 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: index * 0.1 + 0.5 }}
        >
          <h3 className="text-sm font-medium text-white/80 mb-2 tracking-wide uppercase">
            {title}
          </h3>
          <motion.p
            className="text-4xl font-bold text-white mb-4 tracking-tight"
            initial={{ scale: 0.5 }}
            animate={{ scale: 1 }}
            transition={{
              delay: index * 0.1 + 0.7,
              type: "spring",
              stiffness: 200
            }}
          >
            {value}
          </motion.p>

          {change && (
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 + 0.9 }}
              className="flex items-center"
            >
              <motion.span
                animate={{
                  scale: [1, 1.1, 1],
                  transition: {
                    duration: 2,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }
                }}
                className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold backdrop-blur-sm ${
                  change > 0
                    ? 'bg-green-500/20 text-green-100 border border-green-400/30'
                    : 'bg-red-500/20 text-red-100 border border-red-400/30'
                }`}
              >
                <span className="mr-1">
                  {change > 0 ? '↗' : '↘'}
                </span>
                {Math.abs(change)}%
              </motion.span>
              <span className="text-xs text-white/60 ml-2 font-medium">较昨日</span>
            </motion.div>
          )}
        </motion.div>
      </div>
    </motion.div>
  );

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-50 relative overflow-hidden"
    >
      {/* 背景装饰元素 */}
      <div className="absolute inset-0 overflow-hidden">
        <motion.div
          animate={{
            x: [0, 100, 0],
            y: [0, -100, 0],
            rotate: [0, 180, 360],
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "linear"
          }}
          className="absolute -top-40 -left-40 w-80 h-80 bg-gradient-to-r from-blue-400/20 to-purple-400/20 rounded-full blur-3xl"
        />
        <motion.div
          animate={{
            x: [0, -50, 0],
            y: [0, 50, 0],
            rotate: [360, 180, 0],
          }}
          transition={{
            duration: 25,
            repeat: Infinity,
            ease: "linear"
          }}
          className="absolute -bottom-40 -right-40 w-96 h-96 bg-gradient-to-r from-purple-400/20 to-pink-400/20 rounded-full blur-3xl"
        />
      </div>

      <div className="relative z-10 space-y-12 p-8">
        {/* Apple风格头部 */}
        <motion.div
          variants={itemVariants}
          className="text-center space-y-6"
        >
          {/* 时间显示 */}
          <motion.div
            initial={{ scale: 0, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{
              delay: 0.2,
              type: "spring",
              stiffness: 200,
              damping: 20
            }}
            className="inline-flex items-center space-x-4 bg-white/80 backdrop-blur-xl rounded-full px-8 py-4 shadow-2xl border border-white/20"
          >
            <div className="text-center">
              <motion.p
                className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent"
                animate={{
                  backgroundPosition: ["0% 50%", "100% 50%", "0% 50%"],
                }}
                transition={{
                  duration: 3,
                  repeat: Infinity,
                  ease: "linear"
                }}
              >
                {currentTime.toLocaleTimeString('zh-CN', {
                  hour: '2-digit',
                  minute: '2-digit',
                  second: '2-digit'
                })}
              </motion.p>
              <p className="text-sm text-gray-500 font-medium">
                {currentTime.toLocaleDateString('zh-CN', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric',
                  weekday: 'long'
                })}
              </p>
            </div>
          </motion.div>

          {/* 主标题 */}
          <motion.div
            initial={{ y: 50, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{
              delay: 0.4,
              type: "spring",
              stiffness: 100,
              damping: 15
            }}
            className="space-y-4"
          >
            <h1 className="text-6xl font-bold bg-gradient-to-r from-gray-900 via-blue-900 to-purple-900 bg-clip-text text-transparent tracking-tight">
              智能人脸识别系统
            </h1>
            <motion.p
              className="text-xl text-gray-600 max-w-2xl mx-auto leading-relaxed"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.6 }}
            >
              基于先进AI技术的智能识别平台，为您提供安全、高效的身份验证解决方案
            </motion.p>
          </motion.div>

          {/* 状态指示器 */}
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{
              delay: 0.8,
              type: "spring",
              stiffness: 200
            }}
            className="inline-flex items-center space-x-3 bg-green-50 border border-green-200 rounded-full px-6 py-3"
          >
            <motion.div
              animate={{
                scale: [1, 1.2, 1],
                opacity: [1, 0.7, 1]
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                ease: "easeInOut"
              }}
              className="w-3 h-3 bg-green-500 rounded-full"
            />
            <span className="text-green-700 font-medium text-sm">系统运行正常</span>
          </motion.div>
        </motion.div>

      {/* Stats Cards */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
        className="mb-6"
      >
        <h2 className="text-2xl font-bold text-gray-900 mb-2">系统概览</h2>
        <p className="text-gray-600">实时监控系统关键指标</p>
      </motion.div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          icon={UsersIcon}
          title="总用户数"
          value={stats.total_users}
          change={12}
          color="bg-blue-500"
          gradient="rgba(59, 130, 246, 0.9), rgba(37, 99, 235, 0.9)"
          index={0}
        />
        <StatCard
          icon={EyeIcon}
          title="今日识别次数"
          value={stats.today_recognitions}
          change={8}
          color="bg-green-500"
          gradient="rgba(16, 185, 129, 0.9), rgba(5, 150, 105, 0.9)"
          index={1}
        />
        <StatCard
          icon={ArrowTrendingUpIcon}
          title="识别成功率"
          value={`${stats.success_rate}%`}
          change={2.1}
          color="bg-purple-500"
          gradient="rgba(139, 92, 246, 0.9), rgba(124, 58, 237, 0.9)"
          index={2}
        />
        <StatCard
          icon={UserGroupIcon}
          title="活跃用户"
          value={stats.active_users}
          change={-3}
          color="bg-orange-500"
          gradient="rgba(245, 158, 11, 0.9), rgba(217, 119, 6, 0.9)"
          index={3}
        />
      </div>

      {/* Charts Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
        className="mb-6"
      >
        <h2 className="text-2xl font-bold text-gray-900 mb-2">数据分析</h2>
        <p className="text-gray-600">查看详细的使用趋势和统计信息</p>
      </motion.div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Recognition Trends */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6 hover:shadow-xl transition-all duration-300"
        >
          <div className="flex items-center justify-between mb-6">
            <div>
              <h3 className="text-xl font-bold text-gray-900 mb-1">
                本周识别趋势
              </h3>
              <p className="text-sm text-gray-500">每日识别次数统计</p>
            </div>
            <div className="p-3 bg-blue-100 rounded-xl">
              <ChartBarIcon className="w-6 h-6 text-blue-600" />
            </div>
          </div>
          <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-4">
            <ResponsiveContainer width="100%" height={280}>
              <LineChart data={chartData}>
                <CartesianGrid strokeDasharray="3 3" stroke="#e0e7ff" />
                <XAxis
                  dataKey="name"
                  axisLine={false}
                  tickLine={false}
                  tick={{ fill: '#6b7280', fontSize: 12 }}
                />
                <YAxis
                  axisLine={false}
                  tickLine={false}
                  tick={{ fill: '#6b7280', fontSize: 12 }}
                />
                <Tooltip
                  contentStyle={{
                    backgroundColor: 'white',
                    border: 'none',
                    borderRadius: '12px',
                    boxShadow: '0 10px 25px rgba(0,0,0,0.1)'
                  }}
                />
                <Line
                  type="monotone"
                  dataKey="recognitions"
                  stroke="url(#blueGradient)"
                  strokeWidth={3}
                  dot={{ fill: '#3b82f6', strokeWidth: 2, r: 5 }}
                  activeDot={{ r: 7, fill: '#1d4ed8' }}
                />
                <defs>
                  <linearGradient id="blueGradient" x1="0" y1="0" x2="1" y2="0">
                    <stop offset="0%" stopColor="#3b82f6" />
                    <stop offset="100%" stopColor="#1d4ed8" />
                  </linearGradient>
                </defs>
              </LineChart>
            </ResponsiveContainer>
          </div>
        </motion.div>

        {/* Registration Stats */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6 hover:shadow-xl transition-all duration-300"
        >
          <div className="flex items-center justify-between mb-6">
            <div>
              <h3 className="text-xl font-bold text-gray-900 mb-1">
                本周注册统计
              </h3>
              <p className="text-sm text-gray-500">新用户注册趋势</p>
            </div>
            <div className="p-3 bg-green-100 rounded-xl">
              <UsersIcon className="w-6 h-6 text-green-600" />
            </div>
          </div>
          <div className="bg-gradient-to-br from-green-50 to-emerald-50 rounded-xl p-4">
            <ResponsiveContainer width="100%" height={280}>
              <BarChart data={chartData}>
                <CartesianGrid strokeDasharray="3 3" stroke="#d1fae5" />
                <XAxis
                  dataKey="name"
                  axisLine={false}
                  tickLine={false}
                  tick={{ fill: '#6b7280', fontSize: 12 }}
                />
                <YAxis
                  axisLine={false}
                  tickLine={false}
                  tick={{ fill: '#6b7280', fontSize: 12 }}
                />
                <Tooltip
                  contentStyle={{
                    backgroundColor: 'white',
                    border: 'none',
                    borderRadius: '12px',
                    boxShadow: '0 10px 25px rgba(0,0,0,0.1)'
                  }}
                />
                <Bar
                  dataKey="registrations"
                  fill="url(#greenGradient)"
                  radius={[6, 6, 0, 0]}
                />
                <defs>
                  <linearGradient id="greenGradient" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="0%" stopColor="#10b981" />
                    <stop offset="100%" stopColor="#059669" />
                  </linearGradient>
                </defs>
              </BarChart>
            </ResponsiveContainer>
          </div>
        </motion.div>
      </div>

      {/* Recent Activity */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6 hover:shadow-xl transition-all duration-300"
      >
        <div className="flex items-center justify-between mb-6">
          <div>
            <h3 className="text-xl font-bold text-gray-900 mb-1">
              最近活动
            </h3>
            <p className="text-sm text-gray-500">系统实时活动记录</p>
          </div>
          <div className="p-3 bg-purple-100 rounded-xl">
            <ClockIcon className="w-6 h-6 text-purple-600" />
          </div>
        </div>

        <div className="space-y-3">
          {recentActivity.map((activity, index) => (
            <motion.div
              key={activity.id}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 }}
              whileHover={{ x: 5, transition: { duration: 0.2 } }}
              className="group relative overflow-hidden bg-gradient-to-r from-gray-50 to-gray-100 hover:from-blue-50 hover:to-indigo-50 rounded-xl p-4 transition-all duration-300"
            >
              {/* 状态指示条 */}
              <div className={`absolute left-0 top-0 bottom-0 w-1 ${
                activity.status === 'success' ? 'bg-green-500' :
                activity.status === 'error' ? 'bg-red-500' : 'bg-blue-500'
              }`} />

              <div className="flex items-center justify-between ml-4">
                <div className="flex items-center space-x-4">
                  {/* 用户头像 */}
                  <div className={`w-10 h-10 rounded-full flex items-center justify-center text-white font-semibold ${
                    activity.status === 'success' ? 'bg-green-500' :
                    activity.status === 'error' ? 'bg-red-500' : 'bg-blue-500'
                  }`}>
                    {activity.user.charAt(0)}
                  </div>

                  <div>
                    <p className="font-medium text-gray-900 group-hover:text-blue-700 transition-colors">
                      {activity.user}
                    </p>
                    <p className="text-sm text-gray-600 group-hover:text-blue-600 transition-colors">
                      {activity.action}
                    </p>
                    <p className="text-xs text-gray-400 mt-1">
                      {activity.time}
                    </p>
                  </div>
                </div>

                {/* 状态图标 */}
                <div className={`p-2 rounded-lg ${
                  activity.status === 'success' ? 'bg-green-100' :
                  activity.status === 'error' ? 'bg-red-100' : 'bg-blue-100'
                }`}>
                  <CheckCircleIcon className={`w-5 h-5 ${
                    activity.status === 'success' ? 'text-green-600' :
                    activity.status === 'error' ? 'text-red-600' : 'text-blue-600'
                  }`} />
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* 查看更多按钮 */}
        <motion.button
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          className="w-full mt-4 py-3 text-sm font-medium text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-xl transition-all duration-200"
        >
          查看全部活动 →
        </motion.button>
      </motion.div>
      </div>
    </motion.div>
  );
};

export default Dashboard;
